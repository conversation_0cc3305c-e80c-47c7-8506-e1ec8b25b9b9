-- Genişletilmiş E-ticaret Tabloları

-- 1. Users tablosunu olu<PERSON>lım (eğ<PERSON> yoksa)
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  name TEXT,
  image TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  clerk_id TEXT UNIQUE,
  email TEXT,
  phone_number TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  last_login TIMESTAMP WITH TIME ZONE,
  loyalty_points INTEGER DEFAULT 0 CHECK (loyalty_points >= 0),
  wallet_balance DECIMAL(10,2) DEFAULT 0 CHECK (wallet_balance >= 0),
  currency TEXT DEFAULT 'TRY',
  is_admin_approved BOOLEAN DEFAULT false,
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'moderator', 'finance_manager', 'admin')),
  addresses JSONB
);

-- Eğer users tablosu zaten varsa, eks<PERSON> kolonları ekleyelim
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users') THEN
    ALTER TABLE users ADD COLUMN IF NOT EXISTS clerk_id TEXT UNIQUE;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS email TEXT;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number TEXT;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';
    ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS loyalty_points INTEGER DEFAULT 0;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS wallet_balance DECIMAL(10,2) DEFAULT 0;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'TRY';
    ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin_approved BOOLEAN DEFAULT false;
    ALTER TABLE users ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user';
    ALTER TABLE users ADD COLUMN IF NOT EXISTS addresses JSONB;
  END IF;
END $$;

-- Temel e-ticaret tablolarını oluşturalım (eğer yoksa)
CREATE TABLE IF NOT EXISTS categories (
  id TEXT PRIMARY KEY,
  name_tr TEXT NOT NULL,
  name_en TEXT,
  description_tr TEXT,
  description_en TEXT,
  image TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS products (
  id TEXT PRIMARY KEY,
  name_tr TEXT NOT NULL,
  name_en TEXT,
  description_tr TEXT,
  description_en TEXT,
  image TEXT,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  currency TEXT NOT NULL DEFAULT 'TRY',
  stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS product_categories (
  id SERIAL PRIMARY KEY,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  category_id TEXT REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, category_id)
);

CREATE TABLE IF NOT EXISTS orders (
  id TEXT PRIMARY KEY,
  order_number TEXT UNIQUE NOT NULL,
  customer_id TEXT REFERENCES users(id) ON DELETE SET NULL,
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
  currency TEXT NOT NULL DEFAULT 'TRY',
  payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'cancelled')),
  payment_intent_id TEXT,
  order_status TEXT NOT NULL DEFAULT 'pending' CHECK (order_status IN ('pending', 'confirmed', 'shipped', 'delivered', 'cancelled')),
  source TEXT NOT NULL DEFAULT 'regular' CHECK (source IN ('regular', 'giveaway', 'auction')),
  campaign_code TEXT,
  delivery_address JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS order_items (
  id SERIAL PRIMARY KEY,
  order_id TEXT REFERENCES orders(id) ON DELETE CASCADE,
  product_id TEXT REFERENCES products(id) ON DELETE SET NULL,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  currency TEXT NOT NULL DEFAULT 'TRY',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS cart_items (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, product_id)
);

CREATE TABLE IF NOT EXISTS product_reviews (
  id SERIAL PRIMARY KEY,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, user_id)
);

-- 2. Wallet Top-up Requests tablosu
CREATE TABLE IF NOT EXISTS wallet_topup_requests (
  id TEXT PRIMARY KEY,
  request_id TEXT UNIQUE NOT NULL,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL CHECK (amount >= 1 AND amount <= 100000),
  currency TEXT NOT NULL DEFAULT 'TRY',
  payment_method TEXT NOT NULL CHECK (payment_method IN ('credit_card', 'bank_transfer', 'mobile_payment')),
  payment_intent_id TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'approved', 'rejected')),
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITH TIME ZONE
);

-- 3. Sales/Campaigns tablosu
CREATE TABLE IF NOT EXISTS sales_campaigns (
  id TEXT PRIMARY KEY,
  title_tr TEXT NOT NULL,
  title_en TEXT,
  description_tr TEXT,
  description_en TEXT,
  image TEXT,
  discount_type TEXT NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
  discount_amount DECIMAL(10,2) NOT NULL CHECK (discount_amount >= 0),
  currency TEXT DEFAULT 'TRY',
  coupon_code TEXT UNIQUE,
  applicable_to TEXT NOT NULL DEFAULT 'all' CHECK (applicable_to IN ('all', 'giveaway', 'auction', 'regular')),
  validity_from TIMESTAMP WITH TIME ZONE NOT NULL,
  validity_until TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  usage_limit INTEGER DEFAULT 0 CHECK (usage_limit >= 0),
  used_count INTEGER DEFAULT 0 CHECK (used_count >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_dates CHECK (validity_until > validity_from)
);

-- 4. Campaign-Product ilişki tablosu
CREATE TABLE IF NOT EXISTS campaign_products (
  id SERIAL PRIMARY KEY,
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE CASCADE,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, product_id)
);

-- 5. Campaign-Category ilişki tablosu
CREATE TABLE IF NOT EXISTS campaign_categories (
  id SERIAL PRIMARY KEY,
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE CASCADE,
  category_id TEXT REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, category_id)
);

-- 6. Campaign-User ilişki tablosu (hedef kullanıcılar)
CREATE TABLE IF NOT EXISTS campaign_users (
  id SERIAL PRIMARY KEY,
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, user_id)
);

-- 7. Wishlist tablosu
CREATE TABLE IF NOT EXISTS wishlists (
  id SERIAL PRIMARY KEY,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, product_id)
);

-- 8. Product Views tablosu (analytics için)
CREATE TABLE IF NOT EXISTS product_views (
  id SERIAL PRIMARY KEY,
  product_id TEXT REFERENCES products(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Coupons Usage tablosu
CREATE TABLE IF NOT EXISTS coupon_usage (
  id SERIAL PRIMARY KEY,
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  order_id TEXT REFERENCES orders(id) ON DELETE SET NULL,
  discount_applied DECIMAL(10,2) NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(campaign_id, user_id, order_id)
);

-- 10. Auctions tablosu
CREATE TABLE IF NOT EXISTS auctions (
  id TEXT PRIMARY KEY,
  product_id TEXT REFERENCES products(id) ON DELETE SET NULL,
  description_tr TEXT,
  description_en TEXT,
  image TEXT,
  starting_bid DECIMAL(10,2) NOT NULL CHECK (starting_bid >= 0),
  current_bid DECIMAL(10,2) DEFAULT 0 CHECK (current_bid >= 0),
  bid_increment_amount DECIMAL(10,2) NOT NULL DEFAULT 1 CHECK (bid_increment_amount >= 0),
  currency TEXT NOT NULL DEFAULT 'TRY',
  winner_id TEXT REFERENCES users(id) ON DELETE SET NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'cancelled')),
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_auction_dates CHECK (end_time > start_time),
  CONSTRAINT valid_current_bid CHECK (current_bid >= starting_bid)
);

-- 11. Auction Bids tablosu
CREATE TABLE IF NOT EXISTS auction_bids (
  id SERIAL PRIMARY KEY,
  auction_id TEXT REFERENCES auctions(id) ON DELETE CASCADE,
  bidder_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  bid_amount DECIMAL(10,2) NOT NULL CHECK (bid_amount >= 0),
  bid_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_winning_bid BOOLEAN DEFAULT false
);

-- 12. Auction Bidders tablosu (katılımcılar)
CREATE TABLE IF NOT EXISTS auction_bidders (
  id SERIAL PRIMARY KEY,
  auction_id TEXT REFERENCES auctions(id) ON DELETE CASCADE,
  bidder_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(auction_id, bidder_id)
);

-- Indexler
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON users(clerk_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_wallet_requests_user ON wallet_topup_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_requests_status ON wallet_topup_requests(status);
CREATE INDEX IF NOT EXISTS idx_campaigns_active ON sales_campaigns(is_active);
CREATE INDEX IF NOT EXISTS idx_campaigns_validity ON sales_campaigns(validity_from, validity_until);
CREATE INDEX IF NOT EXISTS idx_campaigns_coupon ON sales_campaigns(coupon_code);
CREATE INDEX IF NOT EXISTS idx_wishlists_user ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_product_views_product ON product_views(product_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_campaign ON coupon_usage(campaign_id);
CREATE INDEX IF NOT EXISTS idx_auctions_status ON auctions(status);
CREATE INDEX IF NOT EXISTS idx_auctions_times ON auctions(start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_auction_bids_auction ON auction_bids(auction_id);
CREATE INDEX IF NOT EXISTS idx_auction_bids_bidder ON auction_bids(bidder_id);
CREATE INDEX IF NOT EXISTS idx_auction_bidders_auction ON auction_bidders(auction_id);

-- RLS Politikaları
ALTER TABLE wallet_topup_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE auctions ENABLE ROW LEVEL SECURITY;
ALTER TABLE auction_bids ENABLE ROW LEVEL SECURITY;
ALTER TABLE auction_bidders ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Herkese okuma izni (kampanyalar) - IF NOT EXISTS kullan
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'sales_campaigns' AND policyname = 'Enable read access for active campaigns') THEN
    CREATE POLICY "Enable read access for active campaigns" ON sales_campaigns FOR SELECT USING (is_active = true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'campaign_products' AND policyname = 'Enable read access for campaign products') THEN
    CREATE POLICY "Enable read access for campaign products" ON campaign_products FOR SELECT USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'campaign_categories' AND policyname = 'Enable read access for campaign categories') THEN
    CREATE POLICY "Enable read access for campaign categories" ON campaign_categories FOR SELECT USING (true);
  END IF;
END $$;

-- Kullanıcılar sadece kendi verilerini görebilir - IF NOT EXISTS kullan
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'wallet_topup_requests' AND policyname = 'Users can view own wallet requests') THEN
    CREATE POLICY "Users can view own wallet requests" ON wallet_topup_requests FOR SELECT USING (auth.uid()::text = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'wallet_topup_requests' AND policyname = 'Users can create own wallet requests') THEN
    CREATE POLICY "Users can create own wallet requests" ON wallet_topup_requests FOR INSERT WITH CHECK (auth.uid()::text = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'wishlists' AND policyname = 'Users can manage own wishlist') THEN
    CREATE POLICY "Users can manage own wishlist" ON wishlists FOR ALL USING (auth.uid()::text = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'coupon_usage' AND policyname = 'Users can view own coupon usage') THEN
    CREATE POLICY "Users can view own coupon usage" ON coupon_usage FOR SELECT USING (auth.uid()::text = user_id);
  END IF;
END $$;

-- Product views herkese insert izni (analytics) - IF NOT EXISTS kullan
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_views' AND policyname = 'Anyone can insert product views') THEN
    CREATE POLICY "Anyone can insert product views" ON product_views FOR INSERT WITH CHECK (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_views' AND policyname = 'Enable read access for product views') THEN
    CREATE POLICY "Enable read access for product views" ON product_views FOR SELECT USING (true);
  END IF;

  -- Auction politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'auctions' AND policyname = 'Enable read access for active auctions') THEN
    CREATE POLICY "Enable read access for active auctions" ON auctions FOR SELECT USING (status IN ('active', 'completed'));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'auction_bids' AND policyname = 'Users can view auction bids') THEN
    CREATE POLICY "Users can view auction bids" ON auction_bids FOR SELECT USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'auction_bids' AND policyname = 'Users can insert auction bids') THEN
    CREATE POLICY "Users can insert auction bids" ON auction_bids FOR INSERT WITH CHECK (auth.uid()::text = bidder_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'auction_bidders' AND policyname = 'Users can join auctions') THEN
    CREATE POLICY "Users can join auctions" ON auction_bidders FOR INSERT WITH CHECK (auth.uid()::text = bidder_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'auction_bidders' AND policyname = 'Users can view auction bidders') THEN
    CREATE POLICY "Users can view auction bidders" ON auction_bidders FOR SELECT USING (true);
  END IF;

  -- Cart items politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'cart_items' AND policyname = 'Users can manage own cart') THEN
    CREATE POLICY "Users can manage own cart" ON cart_items FOR ALL USING (auth.uid()::text = user_id);
  END IF;

  -- Product reviews politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_reviews' AND policyname = 'Anyone can read reviews') THEN
    CREATE POLICY "Anyone can read reviews" ON product_reviews FOR SELECT USING (true);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_reviews' AND policyname = 'Users can create own reviews') THEN
    CREATE POLICY "Users can create own reviews" ON product_reviews FOR INSERT WITH CHECK (auth.uid()::text = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_reviews' AND policyname = 'Users can update own reviews') THEN
    CREATE POLICY "Users can update own reviews" ON product_reviews FOR UPDATE USING (auth.uid()::text = user_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'product_reviews' AND policyname = 'Users can delete own reviews') THEN
    CREATE POLICY "Users can delete own reviews" ON product_reviews FOR DELETE USING (auth.uid()::text = user_id);
  END IF;
END $$;
