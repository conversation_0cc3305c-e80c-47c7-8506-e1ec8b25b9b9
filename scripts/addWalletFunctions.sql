-- Add wallet management functions

-- 1. Create wallet topup requests table if not exists
CREATE TABLE IF NOT EXISTS wallet_topup_requests (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    user_id TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'TRY',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    payment_method TEXT DEFAULT 'bank_transfer',
    processed_by TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON>reate function to update user balance
CREATE OR REPLACE FUNCTION update_user_balance(
    user_clerk_id TEXT,
    amount_to_add DECIMAL(10,2)
)
RETURNS VOID AS $$
BEGIN
    UPDATE users 
    SET 
        wallet_balance = COALESCE(wallet_balance, 0) + amount_to_add,
        updated_at = NOW()
    WHERE clerk_id = user_clerk_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found with clerk_id: %', user_clerk_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create function to get user wallet info
CREATE OR REPLACE FUNCTION get_user_wallet(user_clerk_id TEXT)
RETURNS TABLE(
    wallet_balance DECIMAL(10,2),
    currency TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.wallet_balance, u.currency
    FROM users u
    WHERE u.clerk_id = user_clerk_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create trigger to update updated_at on wallet_topup_requests
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_wallet_topup_requests_updated_at ON wallet_topup_requests;
CREATE TRIGGER update_wallet_topup_requests_updated_at
    BEFORE UPDATE ON wallet_topup_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Grant permissions
GRANT ALL ON wallet_topup_requests TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_balance(TEXT, DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_wallet(TEXT) TO authenticated;

-- 6. Add some indexes for performance
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_user_id ON wallet_topup_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_status ON wallet_topup_requests(status);
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_created_at ON wallet_topup_requests(created_at);

-- 7. Show table structure
SELECT 'WALLET_TOPUP_REQUESTS TABLE STRUCTURE' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'wallet_topup_requests' 
ORDER BY ordinal_position;

COMMIT;
