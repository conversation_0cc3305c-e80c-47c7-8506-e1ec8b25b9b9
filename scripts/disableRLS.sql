-- Disable <PERSON><PERSON> for all tables to allow easy development

-- 1. Disable RLS on existing tables only
DO $$
BEGIN
    -- Categories
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Products
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        ALTER TABLE products DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Users
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        ALTER TABLE users DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Admin activity log
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_activity_log') THEN
        ALTER TABLE admin_activity_log DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Orders
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Order items
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        ALTER TABLE order_items DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Cart items
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'cart_items') THEN
        ALTER TABLE cart_items DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Giveaways
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'giveaways') THEN
        ALTER TABLE giveaways DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Auctions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'auctions') THEN
        ALTER TABLE auctions DISABLE ROW LEVEL SECURITY;
    END IF;

    -- Wallet topup requests
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'wallet_topup_requests') THEN
        ALTER TABLE wallet_topup_requests DISABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- 2. Drop all existing policies
DROP POLICY IF EXISTS "categories_select_policy" ON categories;
DROP POLICY IF EXISTS "categories_insert_policy" ON categories;
DROP POLICY IF EXISTS "categories_update_policy" ON categories;
DROP POLICY IF EXISTS "categories_delete_policy" ON categories;

DROP POLICY IF EXISTS "products_select_policy" ON products;
DROP POLICY IF EXISTS "products_insert_policy" ON products;
DROP POLICY IF EXISTS "products_update_policy" ON products;
DROP POLICY IF EXISTS "products_delete_policy" ON products;

DROP POLICY IF EXISTS "users_select_policy" ON users;
DROP POLICY IF EXISTS "users_insert_policy" ON users;
DROP POLICY IF EXISTS "users_update_policy" ON users;

DROP POLICY IF EXISTS "admin_activity_log_select_policy" ON admin_activity_log;
DROP POLICY IF EXISTS "admin_activity_log_insert_policy" ON admin_activity_log;

-- 3. Grant full access to authenticated users
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- 4. Grant permissions on existing tables only
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        GRANT ALL ON categories TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        GRANT ALL ON products TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
        GRANT ALL ON users TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_activity_log') THEN
        GRANT ALL ON admin_activity_log TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        GRANT ALL ON orders TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        GRANT ALL ON order_items TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'cart_items') THEN
        GRANT ALL ON cart_items TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'giveaways') THEN
        GRANT ALL ON giveaways TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'auctions') THEN
        GRANT ALL ON auctions TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'wallet_topup_requests') THEN
        GRANT ALL ON wallet_topup_requests TO authenticated;
    END IF;
END $$;

COMMIT;
