-- Fix Category RLS Policies

-- 1. Drop existing policies if they exist
DROP POLICY IF EXISTS "categories_select_policy" ON categories;
DROP POLICY IF EXISTS "categories_insert_policy" ON categories;
DROP POLICY IF EXISTS "categories_update_policy" ON categories;
DROP POLICY IF EXISTS "categories_delete_policy" ON categories;

-- 2. Enable RLS on categories table
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 3. Create comprehensive RLS policies for categories

-- SELECT policy - Everyone can read active categories, admins can read all
CREATE POLICY "categories_select_policy" ON categories
FOR SELECT USING (
    is_active = true OR 
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND users.role IN ('admin', 'moderator', 'finance_manager')
    )
);

-- INSERT policy - Only admins and users with manage_categories permission
CREATE POLICY "categories_insert_policy" ON categories
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'manage_categories' = ANY(users.role_permissions)
        )
    )
);

-- UPDATE policy - Only admins and users with manage_categories permission
CREATE POLICY "categories_update_policy" ON categories
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'manage_categories' = ANY(users.role_permissions)
        )
    )
) WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'manage_categories' = ANY(users.role_permissions)
        )
    )
);

-- DELETE policy - Only admins and users with manage_categories permission
CREATE POLICY "categories_delete_policy" ON categories
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'manage_categories' = ANY(users.role_permissions)
        )
    )
);

-- 4. Fix Products RLS policies as well

-- Drop existing product policies
DROP POLICY IF EXISTS "products_select_policy" ON products;
DROP POLICY IF EXISTS "products_insert_policy" ON products;
DROP POLICY IF EXISTS "products_update_policy" ON products;
DROP POLICY IF EXISTS "products_delete_policy" ON products;

-- Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- SELECT policy - Everyone can read available products, admins can read all
CREATE POLICY "products_select_policy" ON products
FOR SELECT USING (
    (is_available = true AND is_private = false) OR 
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND users.role IN ('admin', 'moderator', 'finance_manager')
    )
);

-- INSERT policy - Only admins and users with create_products permission
CREATE POLICY "products_insert_policy" ON products
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'create_products' = ANY(users.role_permissions)
        )
    )
);

-- UPDATE policy - Only admins and users with update_products permission
CREATE POLICY "products_update_policy" ON products
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'update_products' = ANY(users.role_permissions)
        )
    )
) WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'update_products' = ANY(users.role_permissions)
        )
    )
);

-- DELETE policy - Only admins and users with delete_products permission
CREATE POLICY "products_delete_policy" ON products
FOR DELETE USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND (
            users.role = 'admin' OR 
            'delete_products' = ANY(users.role_permissions)
        )
    )
);

-- 5. Fix Admin Activity Log RLS
DROP POLICY IF EXISTS "admin_activity_log_policy" ON admin_activity_log;

CREATE POLICY "admin_activity_log_select_policy" ON admin_activity_log
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND users.role IN ('admin', 'moderator', 'finance_manager')
    )
);

CREATE POLICY "admin_activity_log_insert_policy" ON admin_activity_log
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.clerk_id = auth.jwt() ->> 'sub' 
        AND users.role IN ('admin', 'moderator', 'finance_manager')
    )
);

-- 6. Ensure users table has proper RLS
DROP POLICY IF EXISTS "users_select_policy" ON users;
DROP POLICY IF EXISTS "users_insert_policy" ON users;
DROP POLICY IF EXISTS "users_update_policy" ON users;

-- Users can read their own data, admins can read all
CREATE POLICY "users_select_policy" ON users
FOR SELECT USING (
    clerk_id = auth.jwt() ->> 'sub' OR
    EXISTS (
        SELECT 1 FROM users u2
        WHERE u2.clerk_id = auth.jwt() ->> 'sub' 
        AND u2.role IN ('admin', 'moderator', 'finance_manager')
    )
);

-- Users can insert their own data
CREATE POLICY "users_insert_policy" ON users
FOR INSERT WITH CHECK (
    clerk_id = auth.jwt() ->> 'sub'
);

-- Users can update their own data, admins can update all
CREATE POLICY "users_update_policy" ON users
FOR UPDATE USING (
    clerk_id = auth.jwt() ->> 'sub' OR
    EXISTS (
        SELECT 1 FROM users u2
        WHERE u2.clerk_id = auth.jwt() ->> 'sub' 
        AND u2.role = 'admin'
    )
) WITH CHECK (
    clerk_id = auth.jwt() ->> 'sub' OR
    EXISTS (
        SELECT 1 FROM users u2
        WHERE u2.clerk_id = auth.jwt() ->> 'sub' 
        AND u2.role = 'admin'
    )
);

-- 7. Add default admin permissions to existing admin users
UPDATE users 
SET role_permissions = ARRAY[
    'read_products', 'create_products', 'update_products', 'delete_products',
    'manage_all_orders', 'manage_giveaways', 'manage_auctions', 
    'manage_all_wallets', 'manage_users', 'manage_categories',
    'view_admin_dashboard', 'manage_system_settings'
]
WHERE role = 'admin' AND (role_permissions IS NULL OR array_length(role_permissions, 1) IS NULL);

-- 8. Grant necessary permissions
GRANT ALL ON categories TO authenticated;
GRANT ALL ON products TO authenticated;
GRANT ALL ON admin_activity_log TO authenticated;
GRANT ALL ON users TO authenticated;

-- 9. Create helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE clerk_id = auth.jwt() ->> 'sub' 
        AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Create helper function to check user permission
CREATE OR REPLACE FUNCTION has_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE clerk_id = auth.jwt() ->> 'sub' 
        AND (
            role = 'admin' OR 
            permission_name = ANY(role_permissions)
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION has_permission(TEXT) TO authenticated;

COMMIT;
