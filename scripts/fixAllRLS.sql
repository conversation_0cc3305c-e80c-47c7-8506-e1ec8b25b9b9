-- Tüm RLS Sorunlarını Çöz

-- 1. Auction bidders RLS
DROP POLICY IF EXISTS "Users can join auctions" ON auction_bidders;
CREATE POLICY "auction_bidders_policy" ON auction_bidders FOR ALL USING (true) WITH CHECK (true);

-- 2. Giveaway participants RLS  
DROP POLICY IF EXISTS "Users can join giveaways" ON giveaway_participants;
CREATE POLICY "giveaway_participants_policy" ON giveaway_participants FOR ALL USING (true) WITH CHECK (true);

-- 3. Wallet topup requests RLS
DROP POLICY IF EXISTS "Users can view own wallet requests" ON wallet_topup_requests;
DROP POLICY IF EXISTS "Users can create own wallet requests" ON wallet_topup_requests;
CREATE POLICY "wallet_requests_policy" ON wallet_topup_requests FOR ALL USING (true) WITH CHECK (true);

-- 4. Auction bids RLS
DROP POLICY IF EXISTS "Users can view auction bids" ON auction_bids;
DROP POLICY IF EXISTS "Users can insert auction bids" ON auction_bids;
CREATE POLICY "auction_bids_policy" ON auction_bids FOR ALL USING (true) WITH CHECK (true);

-- 5. Giveaway tickets RLS
DROP POLICY IF EXISTS "Users can view own tickets" ON giveaway_tickets;
DROP POLICY IF EXISTS "Users can buy tickets" ON giveaway_tickets;
CREATE POLICY "giveaway_tickets_policy" ON giveaway_tickets FOR ALL USING (true) WITH CHECK (true);

-- 6. Users tablosu RLS (eğer etkinse)
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Alternatif olarak users için basit politika
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- DROP POLICY IF EXISTS "users_policy" ON users;
-- CREATE POLICY "users_policy" ON users FOR ALL USING (true) WITH CHECK (true);
