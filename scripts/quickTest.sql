-- Quick test to disable <PERSON><PERSON> and check basic functionality

-- 1. Disable <PERSON><PERSON> temporarily
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log DISABLE ROW LEVEL SECURITY;

-- 2. <PERSON> permissions
GRANT ALL ON categories TO authenticated;
GRANT ALL ON products TO authenticated;
GRANT ALL ON users TO authenticated;
GRANT ALL ON admin_activity_log TO authenticated;

-- 3. Check if tables exist and have data
SELECT 'categories' as table_name, count(*) as row_count FROM categories
UNION ALL
SELECT 'products' as table_name, count(*) as row_count FROM products
UNION ALL
SELECT 'users' as table_name, count(*) as row_count FROM users;

-- 4. Add basic test data if needed
INSERT INTO categories (id, name_tr, image, is_active) 
VALUES (gen_random_uuid(), 'Test Kategori', 'https://via.placeholder.com/150', true)
ON CONFLICT DO NOTHING;

COMMIT;
