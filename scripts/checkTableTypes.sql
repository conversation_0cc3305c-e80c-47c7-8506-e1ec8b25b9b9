-- Check table structures and fix type mismatches

-- 1. Check categories table structure
SELECT 'CATEGORIES TABLE' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'categories' 
ORDER BY ordinal_position;

-- 2. Check products table structure  
SELECT 'PRODUCTS TABLE' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 3. Check if category_id column exists in products
SELECT 'CATEGORY_ID CHECK' as info;
SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'category_id'
) as category_id_exists;

-- 4. Show sample data from both tables
SELECT 'SAMPLE CATEGORIES' as info;
SELECT id, name_tr, pg_typeof(id) as id_type FROM categories LIMIT 3;

SELECT 'SAMPLE PRODUCTS' as info;
SELECT id, name_tr, pg_typeof(id) as id_type FROM products LIMIT 3;
