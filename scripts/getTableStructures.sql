-- Get table structures for form creation

-- 1. Categories table structure
SELECT 'CATEGORIES TABLE COLUMNS' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'categories' 
ORDER BY ordinal_position;

-- 2. Products table structure
SELECT 'PRODUCTS TABLE COLUMNS' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 3. Show sample data
SELECT 'SAMPLE CATEGORY' as info;
SELECT * FROM categories LIMIT 1;

SELECT 'SAMPLE PRODUCT' as info;
SELECT * FROM products LIMIT 1;
