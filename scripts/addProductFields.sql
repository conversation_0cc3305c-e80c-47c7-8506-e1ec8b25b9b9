-- <PERSON><PERSON><PERSON><PERSON> tablosuna yeni alanlar ekle

-- 1. Products tablosuna yeni kolonlar ekle
DO $$ 
BEGIN
    -- <PERSON><PERSON> çıkan ürün
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_featured') THEN
        ALTER TABLE products ADD COLUMN is_featured BOOLEAN DEFAULT false;
    END IF;
    
    -- <PERSON><PERSON> ürün (private)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_private') THEN
        ALTER TABLE products ADD COLUMN is_private BOOLEAN DEFAULT false;
    END IF;
    
    -- <PERSON>tiketler
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'tags') THEN
        ALTER TABLE products ADD COLUMN tags TEXT[];
    END IF;
    
    -- <PERSON>ğ<PERSON>rl<PERSON>k
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'weight') THEN
        ALTER TABLE products ADD COLUMN weight DECIMAL(10,2);
    END IF;
    
    -- Boyutlar
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'dimensions') THEN
        ALTER TABLE products ADD COLUMN dimensions TEXT;
    END IF;
    
    -- Marka
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'brand') THEN
        ALTER TABLE products ADD COLUMN brand TEXT;
    END IF;
    
    -- Model
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'model') THEN
        ALTER TABLE products ADD COLUMN model TEXT;
    END IF;
    
    -- Garanti (ay)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'warranty_months') THEN
        ALTER TABLE products ADD COLUMN warranty_months INTEGER;
    END IF;
    
    -- SKU (Stok Kodu)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'sku') THEN
        ALTER TABLE products ADD COLUMN sku TEXT UNIQUE;
    END IF;
    
    -- Minimum stok seviyesi
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'min_stock_level') THEN
        ALTER TABLE products ADD COLUMN min_stock_level INTEGER DEFAULT 0;
    END IF;
    
    -- Maksimum stok seviyesi
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'max_stock_level') THEN
        ALTER TABLE products ADD COLUMN max_stock_level INTEGER;
    END IF;
    
    -- Satış sayısı
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'sales_count') THEN
        ALTER TABLE products ADD COLUMN sales_count INTEGER DEFAULT 0;
    END IF;
    
    -- Görüntülenme sayısı
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'view_count') THEN
        ALTER TABLE products ADD COLUMN view_count INTEGER DEFAULT 0;
    END IF;
    
    -- Son güncelleme tarihi
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'updated_at') THEN
        ALTER TABLE products ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- 2. Categories tablosuna yeni kolonlar ekle
DO $$ 
BEGIN
    -- Kategori açıklaması (TR)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'description_tr') THEN
        ALTER TABLE categories ADD COLUMN description_tr TEXT;
    END IF;
    
    -- Kategori açıklaması (EN)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'description_en') THEN
        ALTER TABLE categories ADD COLUMN description_en TEXT;
    END IF;
    
    -- Kategori aktif/pasif
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'is_active') THEN
        ALTER TABLE categories ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
    
    -- Kategori sıralaması
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'sort_order') THEN
        ALTER TABLE categories ADD COLUMN sort_order INTEGER DEFAULT 0;
    END IF;
    
    -- Parent kategori (alt kategoriler için)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'parent_id') THEN
        ALTER TABLE categories ADD COLUMN parent_id TEXT REFERENCES categories(id) ON DELETE SET NULL;
    END IF;
    
    -- Kategori ikonu
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'icon') THEN
        ALTER TABLE categories ADD COLUMN icon TEXT;
    END IF;
    
    -- SEO slug
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'slug') THEN
        ALTER TABLE categories ADD COLUMN slug TEXT UNIQUE;
    END IF;

    -- Son güncelleme tarihi (categories için)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'categories' AND column_name = 'updated_at') THEN
        ALTER TABLE categories ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- 3. İndeksler oluştur
CREATE INDEX IF NOT EXISTS idx_products_is_featured ON products(is_featured);
CREATE INDEX IF NOT EXISTS idx_products_is_private ON products(is_private);
CREATE INDEX IF NOT EXISTS idx_products_brand ON products(brand);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_products_sales_count ON products(sales_count DESC);
CREATE INDEX IF NOT EXISTS idx_products_view_count ON products(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_products_updated_at ON products(updated_at);

CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- 4. Trigger oluştur - updated_at otomatik güncelleme
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Products tablosu için trigger
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Categories tablosu için trigger
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Varsayılan değerler ve örnek veriler
-- Mevcut ürünler için SKU oluştur
WITH numbered_products AS (
    SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as row_num
    FROM products
    WHERE sku IS NULL
)
UPDATE products
SET sku = 'PRD-' || LPAD(numbered_products.row_num::TEXT, 6, '0')
FROM numbered_products
WHERE products.id = numbered_products.id;

-- Mevcut kategoriler için slug oluştur
UPDATE categories 
SET slug = LOWER(REPLACE(REPLACE(name_tr, ' ', '-'), 'ı', 'i'))
WHERE slug IS NULL;

-- 6. Örnek featured ürünler (en pahalı 5 ürünü featured yap)
UPDATE products 
SET is_featured = true 
WHERE id IN (
    SELECT id 
    FROM products 
    WHERE is_available = true 
    ORDER BY price DESC 
    LIMIT 5
);

-- 7. Örnek etiketler ekle
UPDATE products SET tags = ARRAY['elektronik', 'popüler'] WHERE name_tr ILIKE '%telefon%';
UPDATE products SET tags = ARRAY['elektronik', 'bilgisayar'] WHERE name_tr ILIKE '%laptop%';
UPDATE products SET tags = ARRAY['giyim', 'moda'] WHERE name_tr ILIKE '%tişört%' OR name_tr ILIKE '%pantolon%';
UPDATE products SET tags = ARRAY['ev', 'dekorasyon'] WHERE name_tr ILIKE '%masa%' OR name_tr ILIKE '%sandalye%';

-- 8. Örnek marka bilgileri
UPDATE products SET brand = 'Apple' WHERE name_tr ILIKE '%iphone%' OR name_tr ILIKE '%macbook%';
UPDATE products SET brand = 'Samsung' WHERE name_tr ILIKE '%galaxy%' OR name_tr ILIKE '%samsung%';
UPDATE products SET brand = 'Nike' WHERE name_tr ILIKE '%nike%';
UPDATE products SET brand = 'Adidas' WHERE name_tr ILIKE '%adidas%';

-- 9. Örnek garanti süreleri
UPDATE products SET warranty_months = 24 WHERE brand IN ('Apple', 'Samsung');
UPDATE products SET warranty_months = 12 WHERE brand NOT IN ('Apple', 'Samsung') AND name_tr ILIKE '%elektronik%';
UPDATE products SET warranty_months = 6 WHERE warranty_months IS NULL AND price > 100;

COMMIT;
