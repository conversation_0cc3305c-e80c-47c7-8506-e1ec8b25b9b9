const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://owlinhiyrbfpbgbrvjge.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im93bGluaGl5cmJmcGJnYnJ2amdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjk1MjczNiwiZXhwIjoyMDY4NTI4NzM2fQ.isUZbZpPUT7hni9nbrK9fg_GY-V9rhs9zL6XN1EFxAo';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 15 Kullanıcı
const users = [
  { id: 'user-1', name: 'ReactMaster', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-2', name: 'JSNinja', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-3', name: 'WebDevPro', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-4', name: 'CodeGuru', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-5', name: 'FrontendWiz', image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-6', name: 'MobileDev', image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-7', name: 'AIExpert', image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-8', name: 'StartupCEO', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-9', name: 'DevOpsKing', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-10', name: 'DataScientist', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-11', name: 'CloudArchitect', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-12', name: 'SecurityExpert', image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-13', name: 'UIUXDesigner', image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-14', name: 'GameDeveloper', image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-15', name: 'BlockchainDev', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' }
];

// 8 Grup
const groups = [
  { id: 'react-native', name: 'r/ReactNative', image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop' },
  { id: 'javascript', name: 'r/JavaScript', image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop' },
  { id: 'webdev', name: 'r/WebDev', image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=100&h=100&fit=crop' },
  { id: 'programming', name: 'r/Programming', image: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=100&h=100&fit=crop' },
  { id: 'frontend', name: 'r/Frontend', image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=100&h=100&fit=crop' },
  { id: 'mobile', name: 'r/MobileDev', image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=100&h=100&fit=crop' },
  { id: 'ai', name: 'r/AI', image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop' },
  { id: 'startup', name: 'r/Startups', image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=100&h=100&fit=crop' }
];

// 15 Post
const posts = [
  {
    id: 'post-1',
    title: 'React Native 0.76: Game-Changing Performance Updates',
    description: 'The latest React Native release brings incredible performance improvements with the new architecture. Hermes engine optimizations, better memory management, and faster startup times make this a must-upgrade release.',
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=600&fit=crop',
    group_id: 'react-native',
    user_id: 'user-1',
    upvotes: 342,
    nr_of_comments: 45,
    created_at: '2025-01-19T10:30:00Z'
  },
  {
    id: 'post-2',
    title: 'JavaScript ES2025: Revolutionary New Features',
    description: 'Pattern matching, temporal API, and improved async/await syntax are changing how we write JavaScript. These features will revolutionize modern web development.',
    image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=800&h=600&fit=crop',
    group_id: 'javascript',
    user_id: 'user-2',
    upvotes: 289,
    nr_of_comments: 38,
    created_at: '2025-01-19T09:15:00Z'
  },
  {
    id: 'post-3',
    title: 'Building Modern Web Apps with Next.js 15',
    description: 'Exploring the powerful new features in Next.js 15 including improved performance, better developer experience, and enhanced deployment options.',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop',
    group_id: 'webdev',
    user_id: 'user-3',
    upvotes: 234,
    nr_of_comments: 29,
    created_at: '2025-01-19T08:45:00Z'
  },
  {
    id: 'post-4',
    title: 'Clean Code Principles That Changed My Career',
    description: 'After 10 years of programming, these clean code principles transformed how I write software. Essential reading for any developer looking to level up.',
    image: null,
    group_id: 'programming',
    user_id: 'user-4',
    upvotes: 456,
    nr_of_comments: 67,
    created_at: '2025-01-19T07:20:00Z'
  },
  {
    id: 'post-5',
    title: 'State Management Wars: Redux vs Zustand vs Context',
    description: 'Comprehensive comparison of React state management solutions. Performance benchmarks, developer experience, and real-world use cases.',
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',
    group_id: 'frontend',
    user_id: 'user-5',
    upvotes: 198,
    nr_of_comments: 34,
    created_at: '2025-01-18T16:30:00Z'
  },
  {
    id: 'post-6',
    title: 'Flutter vs React Native: 2025 Ultimate Comparison',
    description: 'Updated analysis of cross-platform mobile frameworks. Performance tests, developer productivity, and ecosystem comparison.',
    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop',
    group_id: 'mobile',
    user_id: 'user-6',
    upvotes: 367,
    nr_of_comments: 52,
    created_at: '2025-01-18T15:10:00Z'
  },
  {
    id: 'post-7',
    title: 'Building AI Apps with GPT-4: Complete Guide',
    description: 'Step-by-step tutorial for integrating AI into your applications. Real examples, best practices, and cost optimization strategies.',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
    group_id: 'ai',
    user_id: 'user-7',
    upvotes: 523,
    nr_of_comments: 78,
    created_at: '2025-01-18T14:25:00Z'
  },
  {
    id: 'post-8',
    title: 'From Zero to Unicorn: My Startup Journey',
    description: 'How we built a $1B startup from a garage. Technical decisions, team building, and lessons learned along the way.',
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=600&fit=crop',
    group_id: 'startup',
    user_id: 'user-8',
    upvotes: 789,
    nr_of_comments: 123,
    created_at: '2025-01-18T13:40:00Z'
  },
  {
    id: 'post-9',
    title: 'React Native Performance: 10x Speed Improvements',
    description: 'Advanced optimization techniques that made our app 10x faster. Memory management, rendering optimization, and bundle size reduction.',
    image: null,
    group_id: 'react-native',
    user_id: 'user-9',
    upvotes: 445,
    nr_of_comments: 56,
    created_at: '2025-01-18T12:15:00Z'
  },
  {
    id: 'post-10',
    title: 'Async/Await Patterns That Will Blow Your Mind',
    description: 'Advanced JavaScript async patterns for handling complex asynchronous operations. Error handling, parallel execution, and performance tips.',
    image: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=600&fit=crop',
    group_id: 'javascript',
    user_id: 'user-10',
    upvotes: 234,
    nr_of_comments: 41,
    created_at: '2025-01-18T11:30:00Z'
  },
  {
    id: 'post-11',
    title: 'CSS Grid vs Flexbox: When to Use What',
    description: 'Definitive guide to modern CSS layout. Real-world examples, browser support, and performance considerations.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
    group_id: 'webdev',
    user_id: 'user-11',
    upvotes: 167,
    nr_of_comments: 23,
    created_at: '2025-01-18T10:45:00Z'
  },
  {
    id: 'post-12',
    title: 'Design Patterns Every Developer Must Know',
    description: 'Essential software design patterns with modern examples. Singleton, Factory, Observer, and more with TypeScript implementations.',
    image: null,
    group_id: 'programming',
    user_id: 'user-12',
    upvotes: 356,
    nr_of_comments: 48,
    created_at: '2025-01-18T09:20:00Z'
  },
  {
    id: 'post-13',
    title: 'Micro-Frontends: Architecture for Scale',
    description: 'How we scaled our frontend to 100+ developers using micro-frontend architecture. Tools, patterns, and lessons learned.',
    image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=600&fit=crop',
    group_id: 'frontend',
    user_id: 'user-13',
    upvotes: 278,
    nr_of_comments: 35,
    created_at: '2025-01-18T08:35:00Z'
  },
  {
    id: 'post-14',
    title: 'Building Offline-First Mobile Apps',
    description: 'Complete guide to creating mobile apps that work seamlessly offline. Data sync, caching strategies, and user experience design.',
    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',
    group_id: 'mobile',
    user_id: 'user-14',
    upvotes: 189,
    nr_of_comments: 27,
    created_at: '2025-01-17T17:50:00Z'
  },
  {
    id: 'post-15',
    title: 'Machine Learning for Web Developers',
    description: 'Getting started with ML as a web developer. TensorFlow.js, practical examples, and real-world applications you can build today.',
    image: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=800&h=600&fit=crop',
    group_id: 'ai',
    user_id: 'user-15',
    upvotes: 412,
    nr_of_comments: 59,
    created_at: '2025-01-17T16:25:00Z'
  }
];

// Comments
const comments = [
  // Post 1 comments
  { id: 'comment-1-1', post_id: 'post-1', user_id: 'user-2', parent_id: null, comment: 'Amazing performance improvements! The new architecture is a game changer.', upvotes: 23, created_at: '2025-01-19T11:00:00Z' },
  { id: 'comment-1-2', post_id: 'post-1', user_id: 'user-3', parent_id: 'comment-1-1', comment: 'Totally agree! Our app startup time improved by 40%.', upvotes: 15, created_at: '2025-01-19T11:15:00Z' },
  
  // Post 2 comments
  { id: 'comment-2-1', post_id: 'post-2', user_id: 'user-4', parent_id: null, comment: 'Pattern matching will revolutionize how we handle complex data structures.', upvotes: 18, created_at: '2025-01-19T09:45:00Z' },
  { id: 'comment-2-2', post_id: 'post-2', user_id: 'user-5', parent_id: null, comment: 'The temporal API is long overdue. Finally proper date handling!', upvotes: 12, created_at: '2025-01-19T10:20:00Z' },
  
  // Post 3 comments
  { id: 'comment-3-1', post_id: 'post-3', user_id: 'user-6', parent_id: null, comment: 'Next.js 15 deployment improvements are incredible.', upvotes: 9, created_at: '2025-01-19T09:00:00Z' },
  
  // Post 4 comments
  { id: 'comment-4-1', post_id: 'post-4', user_id: 'user-7', parent_id: null, comment: 'Clean code principles saved my career too. Essential reading!', upvotes: 31, created_at: '2025-01-19T07:45:00Z' },
  { id: 'comment-4-2', post_id: 'post-4', user_id: 'user-8', parent_id: 'comment-4-1', comment: 'Same here! Code reviews became so much easier.', upvotes: 19, created_at: '2025-01-19T08:00:00Z' },
  
  // Post 5 comments
  { id: 'comment-5-1', post_id: 'post-5', user_id: 'user-9', parent_id: null, comment: 'Zustand is my go-to for smaller projects. So simple!', upvotes: 14, created_at: '2025-01-18T17:00:00Z' },
  
  // Post 6 comments
  { id: 'comment-6-1', post_id: 'post-6', user_id: 'user-10', parent_id: null, comment: 'Great comparison! Both frameworks have evolved so much.', upvotes: 22, created_at: '2025-01-18T15:30:00Z' },
  
  // Post 7 comments
  { id: 'comment-7-1', post_id: 'post-7', user_id: 'user-11', parent_id: null, comment: 'AI integration is the future. This guide is perfect timing!', upvotes: 27, created_at: '2025-01-18T14:50:00Z' },
  
  // Post 8 comments
  { id: 'comment-8-1', post_id: 'post-8', user_id: 'user-12', parent_id: null, comment: 'Inspiring story! The technical decisions section was gold.', upvotes: 45, created_at: '2025-01-18T14:00:00Z' },
  
  // More comments for other posts
  { id: 'comment-9-1', post_id: 'post-9', user_id: 'user-13', parent_id: null, comment: '10x improvement sounds too good to be true, but the techniques are solid.', upvotes: 16, created_at: '2025-01-18T12:30:00Z' },
  { id: 'comment-10-1', post_id: 'post-10', user_id: 'user-14', parent_id: null, comment: 'These async patterns solved my biggest performance bottleneck!', upvotes: 11, created_at: '2025-01-18T11:45:00Z' },
  { id: 'comment-15-1', post_id: 'post-15', user_id: 'user-1', parent_id: null, comment: 'TensorFlow.js examples are exactly what I needed. Thanks!', upvotes: 8, created_at: '2025-01-17T16:45:00Z' }
];

async function completeReset() {
  try {
    console.log('🔥 COMPLETE DATABASE RESET STARTING...');

    // 1. Clear all existing data
    console.log('🗑️ Clearing all existing data...');
    await supabase.from('comments').delete().neq('id', '');
    await supabase.from('posts').delete().neq('id', '');
    await supabase.from('groups').delete().neq('id', '');
    await supabase.from('users').delete().neq('id', '');
    console.log('✅ All data cleared');

    // 2. Insert users
    console.log('👥 Inserting 15 users...');
    const { data: insertedUsers, error: usersError } = await supabase
      .from('users')
      .insert(users)
      .select();

    if (usersError) {
      console.error('❌ Error inserting users:', usersError);
      return;
    }
    console.log(`✅ Inserted ${insertedUsers.length} users`);

    // 3. Insert groups
    console.log('📁 Inserting 8 groups...');
    const { data: insertedGroups, error: groupsError } = await supabase
      .from('groups')
      .insert(groups)
      .select();

    if (groupsError) {
      console.error('❌ Error inserting groups:', groupsError);
      return;
    }
    console.log(`✅ Inserted ${insertedGroups.length} groups`);

    // 4. Insert posts
    console.log('📝 Inserting 15 posts...');
    const { data: insertedPosts, error: postsError } = await supabase
      .from('posts')
      .insert(posts)
      .select();

    if (postsError) {
      console.error('❌ Error inserting posts:', postsError);
      return;
    }
    console.log(`✅ Inserted ${insertedPosts.length} posts`);

    // 5. Insert comments
    console.log('💬 Inserting comments...');
    const { data: insertedComments, error: commentsError } = await supabase
      .from('comments')
      .insert(comments)
      .select();

    if (commentsError) {
      console.error('❌ Error inserting comments:', commentsError);
      return;
    }
    console.log(`✅ Inserted ${insertedComments.length} comments`);

    console.log('🎉 COMPLETE DATABASE RESET SUCCESSFUL!');
    console.log('📊 Summary:');
    console.log(`   - ${insertedUsers.length} users`);
    console.log(`   - ${insertedGroups.length} groups`);
    console.log(`   - ${insertedPosts.length} posts`);
    console.log(`   - ${insertedComments.length} comments`);
    
  } catch (error) {
    console.error('❌ RESET FAILED:', error);
  }
}

completeReset();
