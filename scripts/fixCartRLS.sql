-- Cart RLS sorununu çöz

-- Önce mevcut politikayı kaldır
DROP POLICY IF EXISTS "Users can manage own cart" ON cart_items;

-- <PERSON>ni politikalar oluştur
CREATE POLICY "Users can view own cart items" ON cart_items 
  FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert own cart items" ON cart_items 
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own cart items" ON cart_items 
  FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete own cart items" ON cart_items 
  FOR DELETE USING (auth.uid()::text = user_id);
