-- Simplest wallet fix - no triggers, just basic function

-- 1. Ensure wallet_balance column exists
ALTER TABLE users ADD COLUMN IF NOT EXISTS wallet_balance DECIMAL(10,2) DEFAULT 0.00;

-- 2. Simple function without any triggers or updated_at
CREATE OR REPLACE FUNCTION update_user_balance(
    user_clerk_id TEXT,
    amount_to_add DECIMAL(10,2)
)
R<PERSON>URNS VOID AS $$
BEGIN
    UPDATE users 
    SET wallet_balance = COALESCE(wallet_balance, 0) + amount_to_add
    WHERE clerk_id = user_clerk_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found with clerk_id: %', user_clerk_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION update_user_balance(TEXT, DECIMAL) TO authenticated;

-- 4. Test with your actual clerk_id (replace with your real ID)
-- UPDATE users SET wallet_balance = 100.00 WHERE clerk_id = 'YOUR_CLERK_ID_HERE';

-- 5. Show current user balances
SELECT 'CURRENT USER BALANCES' as info;
SELECT clerk_id, name, wallet_balance, currency FROM users WHERE wallet_balance > 0 OR clerk_id LIKE '%user_%';

COMMIT;
