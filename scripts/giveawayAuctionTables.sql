-- Çekiliş ve Açık Artırma Tabloları

-- 1. Giveaways (<PERSON><PERSON><PERSON><PERSON><PERSON>) tablosu
CREATE TABLE IF NOT EXISTS giveaways (
  id TEXT PRIMARY KEY,
  title_tr TEXT NOT NULL,
  title_en TEXT,
  description_tr TEXT,
  description_en TEXT,
  image TEXT,
  rules_tr TEXT,
  rules_en TEXT,
  campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE SET NULL,
  ticket_price DECIMAL(10,2) NOT NULL CHECK (ticket_price >= 0),
  currency TEXT NOT NULL DEFAULT 'TRY',
  total_tickets INTEGER NOT NULL CHECK (total_tickets >= 1),
  tickets_sold INTEGER DEFAULT 0 CHECK (tickets_sold >= 0),
  ticket_sale_percentage_for_draw INTEGER DEFAULT 80 CHECK (ticket_sale_percentage_for_draw >= 0 AND ticket_sale_percentage_for_draw <= 100),
  numbers_per_card INTEGER DEFAULT 3 CHECK (numbers_per_card >= 1),
  ticket_digit_length INTEGER DEFAULT 3 CHECK (ticket_digit_length >= 3 AND ticket_digit_length <= 6),
  max_tickets_per_user INTEGER DEFAULT 10 CHECK (max_tickets_per_user >= 1),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'cancelled')),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  draw_date TIMESTAMP WITH TIME ZONE,
  winning_numbers TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_giveaway_dates CHECK (end_date > start_date),
  CONSTRAINT valid_draw_date CHECK (draw_date IS NULL OR draw_date >= end_date),
  CONSTRAINT valid_tickets_sold CHECK (tickets_sold <= total_tickets)
);

-- 2. Giveaway Prizes (Çekiliş Ödülleri) tablosu
CREATE TABLE IF NOT EXISTS giveaway_prizes (
  id SERIAL PRIMARY KEY,
  giveaway_id TEXT REFERENCES giveaways(id) ON DELETE CASCADE,
  rank INTEGER NOT NULL CHECK (rank >= 1),
  product_id TEXT REFERENCES products(id) ON DELETE SET NULL,
  title_tr TEXT NOT NULL,
  title_en TEXT,
  description_tr TEXT,
  description_en TEXT,
  value DECIMAL(10,2) CHECK (value >= 0),
  currency TEXT DEFAULT 'TRY',
  image TEXT,
  prize_type TEXT NOT NULL DEFAULT 'product' CHECK (prize_type IN ('product', 'cash', 'gift_card')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(giveaway_id, rank)
);

-- 3. Giveaway Participants (Çekiliş Katılımcıları) tablosu
CREATE TABLE IF NOT EXISTS giveaway_participants (
  id SERIAL PRIMARY KEY,
  giveaway_id TEXT REFERENCES giveaways(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(giveaway_id, user_id)
);

-- 4. Giveaway Tickets (Çekiliş Biletleri) tablosu
CREATE TABLE IF NOT EXISTS giveaway_tickets (
  id SERIAL PRIMARY KEY,
  giveaway_id TEXT REFERENCES giveaways(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  ticket_number TEXT NOT NULL,
  purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  chosen_digit_count INTEGER NOT NULL CHECK (chosen_digit_count >= 1),
  status TEXT NOT NULL DEFAULT 'lost' CHECK (status IN ('won', 'lost')),
  order_id TEXT REFERENCES orders(id) ON DELETE SET NULL,
  UNIQUE(giveaway_id, ticket_number)
);

-- 5. Giveaway Winners (Çekiliş Kazananları) tablosu
CREATE TABLE IF NOT EXISTS giveaway_winners (
  id SERIAL PRIMARY KEY,
  giveaway_id TEXT REFERENCES giveaways(id) ON DELETE CASCADE,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  ticket_number TEXT NOT NULL,
  prize_id INTEGER REFERENCES giveaway_prizes(id) ON DELETE CASCADE,
  rank INTEGER NOT NULL CHECK (rank >= 1),
  announced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(giveaway_id, rank)
);

-- 6. Notifications (Bildirimler) tablosu
CREATE TABLE IF NOT EXISTS notifications (
  id TEXT PRIMARY KEY,
  user_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('giveaway_result', 'auction_update', 'order_status', 'wallet_topup', 'promotion')),
  title_tr TEXT NOT NULL,
  title_en TEXT,
  message_tr TEXT NOT NULL,
  message_en TEXT,
  related_giveaway_id TEXT REFERENCES giveaways(id) ON DELETE SET NULL,
  related_auction_id TEXT REFERENCES auctions(id) ON DELETE SET NULL,
  related_order_id TEXT REFERENCES orders(id) ON DELETE SET NULL,
  related_campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE SET NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Analytics (Analiz) tablosu
CREATE TABLE IF NOT EXISTS analytics (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL CHECK (type IN ('sales', 'giveaway', 'auction', 'user_activity', 'campaign')),
  related_giveaway_id TEXT REFERENCES giveaways(id) ON DELETE SET NULL,
  related_auction_id TEXT REFERENCES auctions(id) ON DELETE SET NULL,
  related_order_id TEXT REFERENCES orders(id) ON DELETE SET NULL,
  related_campaign_id TEXT REFERENCES sales_campaigns(id) ON DELETE SET NULL,
  related_user_id TEXT REFERENCES users(id) ON DELETE SET NULL,
  related_product_id TEXT REFERENCES products(id) ON DELETE SET NULL,
  related_category_id TEXT REFERENCES categories(id) ON DELETE SET NULL,
  time_range_start TIMESTAMP WITH TIME ZONE NOT NULL,
  time_range_end TIMESTAMP WITH TIME ZONE NOT NULL,
  total_revenue DECIMAL(10,2) DEFAULT 0 CHECK (total_revenue >= 0),
  currency TEXT DEFAULT 'TRY',
  participants INTEGER DEFAULT 0 CHECK (participants >= 0),
  conversions INTEGER DEFAULT 0 CHECK (conversions >= 0),
  conversion_rate DECIMAL(5,2) DEFAULT 0 CHECK (conversion_rate >= 0 AND conversion_rate <= 100),
  coupon_usage INTEGER DEFAULT 0 CHECK (coupon_usage >= 0),
  roi DECIMAL(10,2),
  social_shares INTEGER DEFAULT 0 CHECK (social_shares >= 0),
  countries TEXT[],
  user_roles TEXT[],
  loyalty_tiers TEXT[],
  funnel_data JSONB,
  ab_test_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_analytics_dates CHECK (time_range_end > time_range_start)
);

-- Indexler
CREATE INDEX IF NOT EXISTS idx_giveaways_status ON giveaways(status);
CREATE INDEX IF NOT EXISTS idx_giveaways_dates ON giveaways(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_giveaway_prizes_giveaway ON giveaway_prizes(giveaway_id);
CREATE INDEX IF NOT EXISTS idx_giveaway_participants_giveaway ON giveaway_participants(giveaway_id);
CREATE INDEX IF NOT EXISTS idx_giveaway_participants_user ON giveaway_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_giveaway_tickets_giveaway ON giveaway_tickets(giveaway_id);
CREATE INDEX IF NOT EXISTS idx_giveaway_tickets_user ON giveaway_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_giveaway_winners_giveaway ON giveaway_winners(giveaway_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_analytics_type ON analytics(type);
CREATE INDEX IF NOT EXISTS idx_analytics_dates ON analytics(time_range_start, time_range_end);
CREATE INDEX IF NOT EXISTS idx_analytics_revenue ON analytics(total_revenue);

-- RLS Etkinleştir
ALTER TABLE giveaways ENABLE ROW LEVEL SECURITY;
ALTER TABLE giveaway_prizes ENABLE ROW LEVEL SECURITY;
ALTER TABLE giveaway_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE giveaway_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE giveaway_winners ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

-- RLS Politikaları
DO $$ 
BEGIN
  -- Giveaways politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaways' AND policyname = 'Enable read access for active giveaways') THEN
    CREATE POLICY "Enable read access for active giveaways" ON giveaways FOR SELECT USING (status IN ('active', 'completed'));
  END IF;
  
  -- Giveaway prizes politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_prizes' AND policyname = 'Enable read access for giveaway prizes') THEN
    CREATE POLICY "Enable read access for giveaway prizes" ON giveaway_prizes FOR SELECT USING (true);
  END IF;
  
  -- Giveaway participants politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_participants' AND policyname = 'Users can join giveaways') THEN
    CREATE POLICY "Users can join giveaways" ON giveaway_participants FOR INSERT WITH CHECK (auth.uid()::text = user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_participants' AND policyname = 'Enable read access for giveaway participants') THEN
    CREATE POLICY "Enable read access for giveaway participants" ON giveaway_participants FOR SELECT USING (true);
  END IF;
  
  -- Giveaway tickets politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_tickets' AND policyname = 'Users can view own tickets') THEN
    CREATE POLICY "Users can view own tickets" ON giveaway_tickets FOR SELECT USING (auth.uid()::text = user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_tickets' AND policyname = 'Users can buy tickets') THEN
    CREATE POLICY "Users can buy tickets" ON giveaway_tickets FOR INSERT WITH CHECK (auth.uid()::text = user_id);
  END IF;
  
  -- Giveaway winners politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'giveaway_winners' AND policyname = 'Enable read access for giveaway winners') THEN
    CREATE POLICY "Enable read access for giveaway winners" ON giveaway_winners FOR SELECT USING (true);
  END IF;
  
  -- Notifications politikaları
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notifications' AND policyname = 'Users can view own notifications') THEN
    CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid()::text = user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'notifications' AND policyname = 'Users can update own notifications') THEN
    CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid()::text = user_id);
  END IF;

  -- Analytics politikaları (sadece okuma)
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'analytics' AND policyname = 'Enable read access for analytics') THEN
    CREATE POLICY "Enable read access for analytics" ON analytics FOR SELECT USING (true);
  END IF;
END $$;
