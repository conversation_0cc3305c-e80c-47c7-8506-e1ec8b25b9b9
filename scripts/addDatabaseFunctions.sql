-- Database Functions for Business Logic

-- 1. Increment product view count function
CREATE OR <PERSON><PERSON>LACE FUNCTION increment_product_view_count(product_id TEXT)
RETURNS void AS $$
BEGIN
    UPDATE products 
    SET view_count = view_count + 1,
        updated_at = NOW()
    WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;

-- 2. Increment product sales count function
CREATE OR REPLACE FUNCTION increment_product_sales_count(product_id TEXT, quantity INTEGER DEFAULT 1)
RETURNS void AS $$
BEGIN
    UPDATE products 
    SET sales_count = sales_count + quantity,
        updated_at = NOW()
    WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;

-- 3. Update product stock function
CREATE OR REPLACE FUNCTION update_product_stock(product_id TEXT, quantity_change INTEGER)
RETURNS void AS $$
BEGIN
    UPDATE products 
    SET stock = stock + quantity_change,
        updated_at = NOW()
    WHERE id = product_id;
    
    -- Check if stock went below zero
    IF (SELECT stock FROM products WHERE id = product_id) < 0 THEN
        RAISE EXCEPTION 'Insufficient stock for product %', product_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 4. Get category with product count
CREATE OR REPLACE FUNCTION get_category_with_product_count(category_id TEXT)
RETURNS TABLE(
    id TEXT,
    name_tr TEXT,
    name_en TEXT,
    description_tr TEXT,
    description_en TEXT,
    image TEXT,
    is_active BOOLEAN,
    parent_id TEXT,
    icon TEXT,
    slug TEXT,
    sort_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    product_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.name_tr,
        c.name_en,
        c.description_tr,
        c.description_en,
        c.image,
        c.is_active,
        c.parent_id,
        c.icon,
        c.slug,
        c.sort_order,
        c.created_at,
        c.updated_at,
        COUNT(p.id) as product_count
    FROM categories c
    LEFT JOIN products p ON c.id = p.category_id AND p.is_available = true
    WHERE c.id = category_id
    GROUP BY c.id, c.name_tr, c.name_en, c.description_tr, c.description_en, 
             c.image, c.is_active, c.parent_id, c.icon, c.slug, c.sort_order, 
             c.created_at, c.updated_at;
END;
$$ LANGUAGE plpgsql;

-- 5. Get product statistics
CREATE OR REPLACE FUNCTION get_product_statistics()
RETURNS TABLE(
    total_products BIGINT,
    active_products BIGINT,
    featured_products BIGINT,
    private_products BIGINT,
    low_stock_products BIGINT,
    out_of_stock_products BIGINT,
    total_value NUMERIC,
    average_price NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_products,
        COUNT(*) FILTER (WHERE is_available = true) as active_products,
        COUNT(*) FILTER (WHERE is_featured = true) as featured_products,
        COUNT(*) FILTER (WHERE is_private = true) as private_products,
        COUNT(*) FILTER (WHERE stock <= min_stock_level AND stock > 0) as low_stock_products,
        COUNT(*) FILTER (WHERE stock = 0) as out_of_stock_products,
        SUM(price * stock) as total_value,
        AVG(price) as average_price
    FROM products;
END;
$$ LANGUAGE plpgsql;

-- 6. Search products with full text search
CREATE OR REPLACE FUNCTION search_products(
    search_term TEXT,
    category_filter TEXT DEFAULT NULL,
    min_price NUMERIC DEFAULT NULL,
    max_price NUMERIC DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id TEXT,
    name_tr TEXT,
    name_en TEXT,
    description_tr TEXT,
    image TEXT,
    price NUMERIC,
    currency TEXT,
    stock INTEGER,
    is_available BOOLEAN,
    is_featured BOOLEAN,
    brand TEXT,
    model TEXT,
    tags TEXT[],
    view_count INTEGER,
    sales_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    category_name TEXT,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name_tr,
        p.name_en,
        p.description_tr,
        p.image,
        p.price,
        p.currency,
        p.stock,
        p.is_available,
        p.is_featured,
        p.brand,
        p.model,
        p.tags,
        p.view_count,
        p.sales_count,
        p.created_at,
        c.name_tr as category_name,
        (
            CASE 
                WHEN p.name_tr ILIKE '%' || search_term || '%' THEN 3.0
                WHEN p.name_en ILIKE '%' || search_term || '%' THEN 2.5
                WHEN p.brand ILIKE '%' || search_term || '%' THEN 2.0
                WHEN p.model ILIKE '%' || search_term || '%' THEN 2.0
                WHEN p.description_tr ILIKE '%' || search_term || '%' THEN 1.5
                WHEN search_term = ANY(p.tags) THEN 2.5
                ELSE 1.0
            END +
            CASE WHEN p.is_featured THEN 0.5 ELSE 0.0 END +
            (p.view_count::REAL / 1000.0) +
            (p.sales_count::REAL / 100.0)
        ) as relevance_score
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE 
        p.is_available = true
        AND (
            p.name_tr ILIKE '%' || search_term || '%' OR
            p.name_en ILIKE '%' || search_term || '%' OR
            p.brand ILIKE '%' || search_term || '%' OR
            p.model ILIKE '%' || search_term || '%' OR
            p.description_tr ILIKE '%' || search_term || '%' OR
            search_term = ANY(p.tags)
        )
        AND (category_filter IS NULL OR p.category_id = category_filter)
        AND (min_price IS NULL OR p.price >= min_price)
        AND (max_price IS NULL OR p.price <= max_price)
    ORDER BY relevance_score DESC, p.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- 7. Get category hierarchy with product counts
CREATE OR REPLACE FUNCTION get_category_hierarchy()
RETURNS TABLE(
    id TEXT,
    name_tr TEXT,
    name_en TEXT,
    image TEXT,
    is_active BOOLEAN,
    parent_id TEXT,
    icon TEXT,
    slug TEXT,
    sort_order INTEGER,
    level INTEGER,
    path TEXT,
    product_count BIGINT,
    active_product_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE category_tree AS (
        -- Base case: root categories
        SELECT 
            c.id,
            c.name_tr,
            c.name_en,
            c.image,
            c.is_active,
            c.parent_id,
            c.icon,
            c.slug,
            c.sort_order,
            0 as level,
            c.name_tr as path
        FROM categories c
        WHERE c.parent_id IS NULL
        
        UNION ALL
        
        -- Recursive case: child categories
        SELECT 
            c.id,
            c.name_tr,
            c.name_en,
            c.image,
            c.is_active,
            c.parent_id,
            c.icon,
            c.slug,
            c.sort_order,
            ct.level + 1,
            ct.path || ' > ' || c.name_tr
        FROM categories c
        INNER JOIN category_tree ct ON c.parent_id = ct.id
    )
    SELECT 
        ct.id,
        ct.name_tr,
        ct.name_en,
        ct.image,
        ct.is_active,
        ct.parent_id,
        ct.icon,
        ct.slug,
        ct.sort_order,
        ct.level,
        ct.path,
        COUNT(p.id) as product_count,
        COUNT(p.id) FILTER (WHERE p.is_available = true) as active_product_count
    FROM category_tree ct
    LEFT JOIN products p ON ct.id = p.category_id
    GROUP BY ct.id, ct.name_tr, ct.name_en, ct.image, ct.is_active, 
             ct.parent_id, ct.icon, ct.slug, ct.sort_order, ct.level, ct.path
    ORDER BY ct.level, ct.sort_order, ct.name_tr;
END;
$$ LANGUAGE plpgsql;

-- 8. Validate category deletion
CREATE OR REPLACE FUNCTION can_delete_category(category_id TEXT)
RETURNS TABLE(
    can_delete BOOLEAN,
    reason TEXT,
    product_count BIGINT,
    subcategory_count BIGINT
) AS $$
DECLARE
    prod_count BIGINT;
    sub_count BIGINT;
BEGIN
    -- Count products in this category
    SELECT COUNT(*) INTO prod_count
    FROM products 
    WHERE category_id = can_delete_category.category_id;
    
    -- Count subcategories
    SELECT COUNT(*) INTO sub_count
    FROM categories 
    WHERE parent_id = can_delete_category.category_id;
    
    -- Determine if deletion is allowed
    IF prod_count > 0 THEN
        RETURN QUERY SELECT false, 'Bu kategoriye ait ' || prod_count || ' ürün bulunmaktadır', prod_count, sub_count;
    ELSIF sub_count > 0 THEN
        RETURN QUERY SELECT false, 'Bu kategorinin ' || sub_count || ' alt kategorisi bulunmaktadır', prod_count, sub_count;
    ELSE
        RETURN QUERY SELECT true, 'Kategori silinebilir', prod_count, sub_count;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 9. Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION increment_product_view_count(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_product_sales_count(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_product_stock(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_category_with_product_count(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_product_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION search_products(TEXT, TEXT, NUMERIC, NUMERIC, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_category_hierarchy() TO authenticated;
GRANT EXECUTE ON FUNCTION can_delete_category(TEXT) TO authenticated;

COMMIT;
