-- Fix users table structure

-- 1. Check current users table structure
SELECT 'CURRENT USERS TABLE STRUCTURE' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- 2. Add missing columns to users table
DO $$
BEGIN
    -- Add updated_at column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE users ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE 'Added updated_at column to users table';
    END IF;

    -- Add wallet_balance column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'wallet_balance'
    ) THEN
        ALTER TABLE users ADD COLUMN wallet_balance DECIMAL(10,2) DEFAULT 0.00;
        RAISE NOTICE 'Added wallet_balance column to users table';
    END IF;

    -- Add currency column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'currency'
    ) THEN
        ALTER TABLE users ADD COLUMN currency TEXT DEFAULT 'TRY';
        RAISE NOTICE 'Added currency column to users table';
    END IF;
END $$;

-- 3. Create or replace the update_user_balance function (without updated_at)
CREATE OR REPLACE FUNCTION update_user_balance(
    user_clerk_id TEXT,
    amount_to_add DECIMAL(10,2)
)
RETURNS VOID AS $$
BEGIN
    UPDATE users 
    SET wallet_balance = COALESCE(wallet_balance, 0) + amount_to_add
    WHERE clerk_id = user_clerk_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found with clerk_id: %', user_clerk_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create trigger for users table updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Grant permissions
GRANT EXECUTE ON FUNCTION update_user_balance(TEXT, DECIMAL) TO authenticated;

-- 6. Show final users table structure
SELECT 'FINAL USERS TABLE STRUCTURE' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- 7. Test the function with a sample (replace with actual clerk_id)
-- SELECT update_user_balance('test_clerk_id', 10.00);

COMMIT;
