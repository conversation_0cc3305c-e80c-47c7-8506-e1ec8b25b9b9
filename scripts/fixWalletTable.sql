-- Fix wallet_topup_requests table structure

-- 1. Check current table structure
SELECT 'CURRENT WALLET_TOPUP_REQUESTS STRUCTURE' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'wallet_topup_requests' 
ORDER BY ordinal_position;

-- 2. Add missing columns if they don't exist
DO $$
BEGIN
    -- Add processed_by column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'wallet_topup_requests' AND column_name = 'processed_by'
    ) THEN
        ALTER TABLE wallet_topup_requests ADD COLUMN processed_by TEXT;
        RAISE NOTICE 'Added processed_by column';
    END IF;

    -- Add processed_at column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'wallet_topup_requests' AND column_name = 'processed_at'
    ) THEN
        ALTER TABLE wallet_topup_requests ADD COLUMN processed_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added processed_at column';
    END IF;

    -- Add updated_at column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'wallet_topup_requests' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE wallet_topup_requests ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE 'Added updated_at column';
    END IF;
END $$;

-- 3. Ensure wallet_balance column exists in users table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'wallet_balance'
    ) THEN
        ALTER TABLE users ADD COLUMN wallet_balance DECIMAL(10,2) DEFAULT 0.00;
        RAISE NOTICE 'Added wallet_balance column to users table';
    END IF;
END $$;

-- 4. Create or replace the update_user_balance function
CREATE OR REPLACE FUNCTION update_user_balance(
    user_clerk_id TEXT,
    amount_to_add DECIMAL(10,2)
)
RETURNS VOID AS $$
BEGIN
    UPDATE users 
    SET 
        wallet_balance = COALESCE(wallet_balance, 0) + amount_to_add,
        updated_at = NOW()
    WHERE clerk_id = user_clerk_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found with clerk_id: %', user_clerk_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Create trigger for wallet_topup_requests
DROP TRIGGER IF EXISTS update_wallet_topup_requests_updated_at ON wallet_topup_requests;
CREATE TRIGGER update_wallet_topup_requests_updated_at
    BEFORE UPDATE ON wallet_topup_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. Grant permissions
GRANT ALL ON wallet_topup_requests TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_balance(TEXT, DECIMAL) TO authenticated;

-- 8. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_user_id ON wallet_topup_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_status ON wallet_topup_requests(status);
CREATE INDEX IF NOT EXISTS idx_wallet_topup_requests_created_at ON wallet_topup_requests(created_at);

-- 9. Show final table structure
SELECT 'FINAL WALLET_TOPUP_REQUESTS STRUCTURE' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'wallet_topup_requests' 
ORDER BY ordinal_position;

-- 10. Show users table wallet columns
SELECT 'USERS TABLE WALLET COLUMNS' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name IN ('wallet_balance', 'currency')
ORDER BY ordinal_position;

COMMIT;
