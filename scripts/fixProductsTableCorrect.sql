-- Fix Products Table - Add category_id with correct type

-- 1. Add category_id column with TEXT type (matching categories.id)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'category_id'
    ) THEN
        ALTER TABLE products ADD COLUMN category_id TEXT REFERENCES categories(id);
        RAISE NOTICE 'Added category_id column (TEXT) to products table';
    ELSE
        RAISE NOTICE 'category_id column already exists in products table';
    END IF;
END $$;

-- 2. Add other missing columns if needed
DO $$
BEGIN
    -- Add sku column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'sku'
    ) THEN
        ALTER TABLE products ADD COLUMN sku VARCHAR(100);
        RAISE NOTICE 'Added sku column to products table';
    END IF;

    -- Add sales_count if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'sales_count'
    ) THEN
        ALTER TABLE products ADD COLUMN sales_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added sales_count column to products table';
    END IF;

    -- Add view_count if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'view_count'
    ) THEN
        ALTER TABLE products ADD COLUMN view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added view_count column to products table';
    END IF;
END $$;

-- 3. Update existing products to have a category_id (assign to first available category)
DO $$
DECLARE
    first_category_id TEXT;
BEGIN
    -- Get first category ID
    SELECT id INTO first_category_id FROM categories LIMIT 1;
    
    IF first_category_id IS NOT NULL THEN
        -- Update products without category_id
        UPDATE products 
        SET category_id = first_category_id 
        WHERE category_id IS NULL;
        
        RAISE NOTICE 'Updated products without category_id to use category: %', first_category_id;
    END IF;
END $$;

-- 4. Show final table structure
SELECT 'FINAL PRODUCTS TABLE STRUCTURE' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 5. Show sample data with category relationship
SELECT 'SAMPLE PRODUCTS WITH CATEGORIES' as info;
SELECT 
    p.id, 
    p.name_tr, 
    p.price, 
    p.stock, 
    p.category_id,
    c.name_tr as category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LIMIT 5;

COMMIT;
