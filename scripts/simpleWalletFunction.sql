-- Simple wallet function without updated_at dependency

-- 1. Create simple update_user_balance function
CREATE OR REPLACE FUNCTION update_user_balance(
    user_clerk_id TEXT,
    amount_to_add DECIMAL(10,2)
)
RETURNS VOID AS $$
BEGIN
    -- First ensure user exists and has wallet_balance column
    UPDATE users 
    SET wallet_balance = COALESCE(wallet_balance, 0) + amount_to_add
    WHERE clerk_id = user_clerk_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found with clerk_id: %', user_clerk_id;
    END IF;
    
    RAISE NOTICE 'Updated balance for user % by %', user_clerk_id, amount_to_add;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Grant permissions
GRANT EXECUTE ON FUNCTION update_user_balance(TEXT, DECIMAL) TO authenticated;

-- 3. Test if wallet_balance column exists in users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'wallet_balance'
    ) THEN
        ALTER TABLE users ADD COLUMN wallet_balance DECIMAL(10,2) DEFAULT 0.00;
        RAISE NOTICE 'Added wallet_balance column to users table';
    ELSE
        RAISE NOTICE 'wallet_balance column already exists in users table';
    END IF;
END $$;

-- 4. Show users table columns related to wallet
SELECT 'USERS WALLET COLUMNS' as info;
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('wallet_balance', 'currency', 'clerk_id', 'id')
ORDER BY ordinal_position;

COMMIT;
