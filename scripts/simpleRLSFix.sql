-- Simple RLS Fix - Disable <PERSON><PERSON> temporarily for testing

-- 1. Disable RLS on all tables temporarily
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log DISABLE ROW LEVEL SECURITY;

-- 2. Drop all existing policies
DROP POLICY IF EXISTS "categories_select_policy" ON categories;
DROP POLICY IF EXISTS "categories_insert_policy" ON categories;
DROP POLICY IF EXISTS "categories_update_policy" ON categories;
DROP POLICY IF EXISTS "categories_delete_policy" ON categories;

DROP POLICY IF EXISTS "products_select_policy" ON products;
DROP POLICY IF EXISTS "products_insert_policy" ON products;
DROP POLICY IF EXISTS "products_update_policy" ON products;
DROP POLICY IF EXISTS "products_delete_policy" ON products;

DROP POLICY IF EXISTS "users_select_policy" ON users;
DROP POLICY IF EXISTS "users_insert_policy" ON users;
DROP POLICY IF EXISTS "users_update_policy" ON users;

DROP POLICY IF EXISTS "admin_activity_log_select_policy" ON admin_activity_log;
DROP POLICY IF EXISTS "admin_activity_log_insert_policy" ON admin_activity_log;

-- 3. Grant full access to authenticated users
GRANT ALL ON categories TO authenticated;
GRANT ALL ON products TO authenticated;
GRANT ALL ON users TO authenticated;
GRANT ALL ON admin_activity_log TO authenticated;

-- 4. Ensure your user exists and is admin
INSERT INTO users (id, clerk_id, name, email, role, role_permissions, wallet_balance, currency)
VALUES (
    gen_random_uuid(),
    'YOUR_CLERK_ID_HERE', -- Replace with your actual Clerk ID
    'Admin User',
    '<EMAIL>',
    'admin',
    ARRAY['read_products', 'create_products', 'update_products', 'delete_products', 'manage_categories'],
    1000.00,
    'TRY'
) ON CONFLICT (clerk_id) DO UPDATE SET
    role = 'admin',
    role_permissions = ARRAY['read_products', 'create_products', 'update_products', 'delete_products', 'manage_categories'];

-- 5. Add some test categories if none exist (only if categories table is empty)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM categories LIMIT 1) THEN
        INSERT INTO categories (id, name_tr, name_en, image, is_active, sort_order)
        VALUES
            (gen_random_uuid(), 'Elektronik', 'Electronics', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300', true, 1),
            (gen_random_uuid(), 'Giyim', 'Clothing', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300', true, 2),
            (gen_random_uuid(), 'Ev & Yaşam', 'Home & Living', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300', true, 3);
    END IF;
END $$;

-- 6. Add some test products if none exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM products LIMIT 1) THEN
        INSERT INTO products (id, name_tr, name_en, description_tr, description_en, image, price, currency, stock, is_available, category_id)
        SELECT
            gen_random_uuid(),
            'Test Ürün ' || generate_series,
            'Test Product ' || generate_series,
            'Bu bir test ürünüdür',
            'This is a test product',
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300',
            (random() * 1000 + 50)::numeric(10,2),
            'TRY',
            (random() * 100 + 1)::integer,
            true,
            (SELECT id FROM categories ORDER BY random() LIMIT 1)
        FROM generate_series(1, 5);
    END IF;
END $$;

COMMIT;
