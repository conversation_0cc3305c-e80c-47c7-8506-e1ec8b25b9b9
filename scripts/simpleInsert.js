// Simple script to insert test data using anon key
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://owlinhiyrbfpbgbrvjge.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im93bGluaGl5cmJmcGJnYnJ2amdlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NTI3MzYsImV4cCI6MjA2ODUyODczNn0.cB_3UiGBjn2Nwfb68hQeaII7FwdqNLbNuVIofsonMN4';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function insertTestData() {
  try {
    console.log('🌱 Inserting test data...');

    // Insert a test group first
    const { data: group, error: groupError } = await supabase
      .from('groups')
      .insert({
        id: 'group-1',
        name: 'r/FrontendDevelopers',
        image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/avatars/11.png'
      })
      .select()
      .single();

    if (groupError && groupError.code !== '23505') { // 23505 is duplicate key error
      console.error('Error inserting group:', groupError);
      return;
    }
    console.log('✅ Group inserted or already exists');

    // Insert a test post
    const { data: post, error: postError } = await supabase
      .from('posts')
      .insert({
        id: 'post-1',
        title: 'Why React Native is the best?',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
        image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/3.jpg',
        created_at: '2025-02-12T08:30:00Z',
        upvotes: 120,
        nr_of_comments: 15,
        group_id: 'group-1',
        user_id: 'user-1'
      })
      .select()
      .single();

    if (postError && postError.code !== '23505') {
      console.error('Error inserting post:', postError);
      return;
    }
    console.log('✅ Post inserted or already exists');

    // Insert test comments
    const comments = [
      {
        id: 'comment-1',
        post_id: 'post-1',
        user_id: 'user-xyz',
        parent_id: null,
        comment: 'Very nice explanation. Detailed, straight to the point and with valid comparisons!',
        created_at: '2025-02-19T12:00:00Z',
        upvotes: 12
      },
      {
        id: 'comment-2',
        post_id: 'post-1',
        user_id: 'user-abc',
        parent_id: 'comment-1',
        comment: 'Totally agree!',
        created_at: '2025-02-19T12:05:00Z',
        upvotes: 18
      }
    ];

    for (const comment of comments) {
      const { error: commentError } = await supabase
        .from('comments')
        .insert(comment);

      if (commentError && commentError.code !== '23505') {
        console.error('Error inserting comment:', commentError);
      }
    }
    console.log('✅ Comments inserted or already exist');

    console.log('🎉 Test data insertion completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

insertTestData();
