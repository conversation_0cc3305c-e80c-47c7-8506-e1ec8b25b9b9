const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabaseUrl = 'https://owlinhiyrbfpbgbrvjge.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im93bGluaGl5cmJmcGJnYnJ2amdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjk1MjczNiwiZXhwIjoyMDY4NTI4NzM2fQ.isUZbZpPUT7hni9nbrK9fg_GY-V9rhs9zL6XN1EFxAo';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Kategoriler
const categories = [
  {
    id: 'CAT-TECH001',
    name_tr: 'Teknoloji',
    name_en: 'Technology',
    description_tr: 'A<PERSON><PERSON><PERSON><PERSON> telefonlar, bilgisayarlar ve teknoloji ürünleri',
    description_en: 'Smartphones, computers and technology products',
    image: 'https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop'
  },
  {
    id: 'CAT-AUDIO001',
    name_tr: 'Ses ve Görüntü',
    name_en: 'Audio & Video',
    description_tr: 'Kulaklıklar, hoparlörler ve ses sistemleri',
    description_en: 'Headphones, speakers and audio systems',
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop'
  },
  {
    id: 'CAT-HOME001',
    name_tr: 'Ev ve Yaşam',
    name_en: 'Home & Living',
    description_tr: 'Ev aletleri ve yaşam ürünleri',
    description_en: 'Home appliances and living products',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'
  },
  {
    id: 'CAT-FASHION001',
    name_tr: 'Moda ve Giyim',
    name_en: 'Fashion & Clothing',
    description_tr: 'Ayakkabılar, kıyafetler ve aksesuar',
    description_en: 'Shoes, clothes and accessories',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop'
  },
  {
    id: 'CAT-GAMING001',
    name_tr: 'Oyun ve Eğlence',
    name_en: 'Gaming & Entertainment',
    description_tr: 'Oyun konsolları ve eğlence ürünleri',
    description_en: 'Gaming consoles and entertainment products',
    image: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=400&h=300&fit=crop'
  }
];

// Products.json'dan ürünleri okuyup Türkçe'ye çevirelim
const productsFromFile = JSON.parse(fs.readFileSync(path.join(__dirname, '../assets/data/products.json'), 'utf8'));

const products = [
  {
    id: 'PRD-AIRPODS001',
    name_tr: 'AirPods Pro',
    name_en: 'AirPods Pro',
    description_tr: 'Apple\'ın kablosuz gürültü önleyici kulaklığı, uyarlanabilir şeffaflık ve uzamsal ses ile.',
    description_en: 'Apple\'s wireless noise-cancelling earbuds with adaptive transparency and spatial audio.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/airpodspro.jpg',
    price: 249.99,
    currency: 'USD',
    stock: 50,
    is_available: true
  },
  {
    id: 'PRD-WATCH001',
    name_tr: 'Apple Watch Series 9',
    name_en: 'Apple Watch Series 9',
    description_tr: 'Apple\'ın her zaman açık retina ekranı ve gelişmiş sağlık izleme özellikli en yeni akıllı saati.',
    description_en: 'Latest smartwatch from Apple featuring always-on retina display and advanced health monitoring.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/applewatch.jpg',
    price: 399.99,
    currency: 'USD',
    stock: 30,
    is_available: true
  },
  {
    id: 'PRD-BOSE001',
    name_tr: 'Bose Gürültü Önleyici Kulaklık',
    name_en: 'Bose Noise Cancelling Headphones',
    description_tr: 'Premium gürültü önleyici kulak üstü kulaklık, 20 saat pil ömrü ve sesli asistan entegrasyonu.',
    description_en: 'Premium noise-cancelling over-ear headphones with 20 hours of battery life and voice assistant integration.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/boseheadphones.jpg',
    price: 349.99,
    currency: 'USD',
    stock: 25,
    is_available: true
  },
  {
    id: 'PRD-DYSON001',
    name_tr: 'Dyson V15 Süpürge',
    name_en: 'Dyson V15 Vacuum',
    description_tr: 'Lazer algılama teknolojisi ve gelişmiş filtrasyon sistemi ile güçlü kablosuz süpürge.',
    description_en: 'Powerful cordless vacuum with laser-detect technology and advanced filtration system.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/dysonvacuum.jpg',
    price: 699.99,
    currency: 'USD',
    stock: 15,
    is_available: true
  },
  {
    id: 'PRD-GALAXY001',
    name_tr: 'Samsung Galaxy S24 Ultra',
    name_en: 'Samsung Galaxy S24 Ultra',
    description_tr: 'Amiral gemisi akıllı telefon, 200MP kamera, 120Hz AMOLED ekran ve S Pen desteği.',
    description_en: 'Flagship smartphone with 200MP camera, 120Hz AMOLED display, and S Pen support.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/galaxys24ultra.jpg',
    price: 1199.99,
    currency: 'USD',
    stock: 20,
    is_available: true
  },
  {
    id: 'PRD-IPAD001',
    name_tr: 'iPad Pro',
    name_en: 'iPad Pro',
    description_tr: '12.9 inç iPad Pro, M2 çip, Liquid Retina XDR ekran ve 5G bağlantısı.',
    description_en: '12.9-inch iPad Pro with M2 chip, Liquid Retina XDR display, and 5G connectivity.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/ipad.jpg',
    price: 1099.99,
    currency: 'USD',
    stock: 18,
    is_available: true
  },
  {
    id: 'PRD-IPHONE001',
    name_tr: 'iPhone 16 Pro',
    name_en: 'iPhone 16 Pro',
    description_tr: 'Apple\'ın A18 Bionic çip, ProMotion ekran ve gelişmiş üçlü lens kamera sistemi ile en yeni iPhone\'u.',
    description_en: 'Apple\'s latest iPhone with A18 Bionic chip, ProMotion display, and advanced triple-lens camera system.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/iphone16pro.jpg',
    price: 1299.99,
    currency: 'USD',
    stock: 35,
    is_available: true
  },
  {
    id: 'PRD-MACBOOK001',
    name_tr: 'MacBook Pro 16-inç',
    name_en: 'MacBook Pro 16-inch',
    description_tr: 'M2 Max çip, Liquid Retina XDR ekran ve 1TB SSD ile yüksek performanslı dizüstü bilgisayar.',
    description_en: 'High-performance laptop with M2 Max chip, Liquid Retina XDR display, and 1TB SSD.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/macbookpro.jpg',
    price: 2499.99,
    currency: 'USD',
    stock: 12,
    is_available: true
  },
  {
    id: 'PRD-NIKE001',
    name_tr: 'Nike Air Max 270',
    name_en: 'Nike Air Max 270',
    description_tr: 'Nike Air yastıklama ve nefes alabilir mesh üst kısım ile popüler koşu ayakkabısı.',
    description_en: 'Popular running shoes with Nike Air cushioning and breathable mesh upper.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/nikeairmax.jpg',
    price: 149.99,
    currency: 'USD',
    stock: 40,
    is_available: true
  },
  {
    id: 'PRD-PS5001',
    name_tr: 'PlayStation 5',
    name_en: 'PlayStation 5',
    description_tr: 'Sony\'nin 8K çıkış, şimşek hızında yükleme süreleri ve 825GB SSD ile en yeni oyun konsolu.',
    description_en: 'Sony\'s latest gaming console with 8K output, lightning-fast load times, and 825GB SSD.',
    image: 'https://notjustdev-dummy.s3.us-east-2.amazonaws.com/ecom/playstation5.jpg',
    price: 499.99,
    currency: 'USD',
    stock: 8,
    is_available: true
  }
];

// Ürün-Kategori ilişkileri
const productCategories = [
  { product_id: 'PRD-AIRPODS001', category_id: 'CAT-AUDIO001' },
  { product_id: 'PRD-WATCH001', category_id: 'CAT-TECH001' },
  { product_id: 'PRD-BOSE001', category_id: 'CAT-AUDIO001' },
  { product_id: 'PRD-DYSON001', category_id: 'CAT-HOME001' },
  { product_id: 'PRD-GALAXY001', category_id: 'CAT-TECH001' },
  { product_id: 'PRD-IPAD001', category_id: 'CAT-TECH001' },
  { product_id: 'PRD-IPHONE001', category_id: 'CAT-TECH001' },
  { product_id: 'PRD-MACBOOK001', category_id: 'CAT-TECH001' },
  { product_id: 'PRD-NIKE001', category_id: 'CAT-FASHION001' },
  { product_id: 'PRD-PS5001', category_id: 'CAT-GAMING001' }
];

async function seedEcommerce() {
  try {
    console.log('🛒 E-ticaret verilerini yükleniyor...');

    // 1. Kategorileri ekle
    console.log('📁 Kategoriler ekleniyor...');
    const { data: insertedCategories, error: categoriesError } = await supabase
      .from('categories')
      .insert(categories)
      .select();

    if (categoriesError) {
      console.error('❌ Kategori ekleme hatası:', categoriesError);
      return;
    }
    console.log(`✅ ${insertedCategories.length} kategori eklendi`);

    // 2. Ürünleri ekle
    console.log('🛍️ Ürünler ekleniyor...');
    const { data: insertedProducts, error: productsError } = await supabase
      .from('products')
      .insert(products)
      .select();

    if (productsError) {
      console.error('❌ Ürün ekleme hatası:', productsError);
      return;
    }
    console.log(`✅ ${insertedProducts.length} ürün eklendi`);

    // 3. Ürün-Kategori ilişkilerini ekle
    console.log('🔗 Ürün-Kategori ilişkileri ekleniyor...');
    const { data: insertedRelations, error: relationsError } = await supabase
      .from('product_categories')
      .insert(productCategories)
      .select();

    if (relationsError) {
      console.error('❌ İlişki ekleme hatası:', relationsError);
      return;
    }
    console.log(`✅ ${insertedRelations.length} ilişki eklendi`);

    console.log('🎉 E-ticaret verileri başarıyla yüklendi!');
    console.log('📊 Özet:');
    console.log(`   - ${insertedCategories.length} kategori`);
    console.log(`   - ${insertedProducts.length} ürün`);
    console.log(`   - ${insertedRelations.length} ürün-kategori ilişkisi`);
    
  } catch (error) {
    console.error('❌ E-ticaret veri yükleme hatası:', error);
  }
}

seedEcommerce();
