const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Service role key needed for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables. Please set EXPO_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Read JSON files
const postsData = JSON.parse(fs.readFileSync(path.join(__dirname, '../assets/data/posts.json'), 'utf8'));
const groupsData = JSON.parse(fs.readFileSync(path.join(__dirname, '../assets/data/groups.json'), 'utf8'));
const commentsData = JSON.parse(fs.readFileSync(path.join(__dirname, '../assets/data/comments.json'), 'utf8'));

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // 1. Insert Groups first (since posts reference groups)
    console.log('📁 Inserting groups...');
    const { data: insertedGroups, error: groupsError } = await supabase
      .from('groups')
      .insert(groupsData)
      .select();

    if (groupsError) {
      console.error('Error inserting groups:', groupsError);
      return;
    }
    console.log(`✅ Inserted ${insertedGroups.length} groups`);

    // 2. Transform and insert Posts
    console.log('📝 Inserting posts...');
    const transformedPosts = postsData.map(post => ({
      id: post.id,
      title: post.title,
      description: post.description,
      image: post.image,
      created_at: post.created_at,
      upvotes: post.upvotes,
      nr_of_comments: post.nr_of_comments,
      group_id: post.group.id,
      user_id: post.user.id
    }));

    const { data: insertedPosts, error: postsError } = await supabase
      .from('posts')
      .insert(transformedPosts)
      .select();

    if (postsError) {
      console.error('Error inserting posts:', postsError);
      return;
    }
    console.log(`✅ Inserted ${insertedPosts.length} posts`);

    // 3. Transform and insert Comments (flatten the nested structure)
    console.log('💬 Inserting comments...');
    const flattenedComments = [];
    
    function flattenComments(comments, parentId = null) {
      comments.forEach(comment => {
        flattenedComments.push({
          id: comment.id,
          post_id: comment.post_id,
          user_id: comment.user_id,
          parent_id: parentId,
          comment: comment.comment,
          created_at: comment.created_at,
          upvotes: comment.upvotes
        });
        
        if (comment.replies && comment.replies.length > 0) {
          flattenComments(comment.replies, comment.id);
        }
      });
    }
    
    flattenComments(commentsData);

    const { data: insertedComments, error: commentsError } = await supabase
      .from('comments')
      .insert(flattenedComments)
      .select();

    if (commentsError) {
      console.error('Error inserting comments:', commentsError);
      return;
    }
    console.log(`✅ Inserted ${insertedComments.length} comments`);

    console.log('🎉 Database seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Run the seeding
seedDatabase();
