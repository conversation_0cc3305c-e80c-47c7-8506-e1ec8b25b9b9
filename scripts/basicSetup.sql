-- Basic setup - just disable <PERSON><PERSON> on core tables

-- 1. Disable RLS on core tables
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- 2. Grant basic permissions
GRANT ALL ON categories TO authenticated;
GRANT ALL ON products TO authenticated;
GRANT ALL ON users TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- 3. Show existing tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

COMMIT;
