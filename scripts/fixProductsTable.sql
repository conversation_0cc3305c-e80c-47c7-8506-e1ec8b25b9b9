-- Fix Products Table - Add missing columns and fix structure

-- 1. Check current products table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 2. Add missing category_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'category_id'
    ) THEN
        ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id);
        RAISE NOTICE 'Added category_id column to products table';
    ELSE
        RAISE NOTICE 'category_id column already exists in products table';
    END IF;
END $$;

-- 3. Ensure other essential columns exist
DO $$
BEGIN
    -- Add sku column if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'sku'
    ) THEN
        ALTER TABLE products ADD COLUMN sku VARCHAR(100) UNIQUE;
        RAISE NOTICE 'Added sku column to products table';
    END IF;

    -- Add sales_count if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'sales_count'
    ) THEN
        ALTER TABLE products ADD COLUMN sales_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added sales_count column to products table';
    END IF;

    -- Add view_count if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'view_count'
    ) THEN
        ALTER TABLE products ADD COLUMN view_count INTEGER DEFAULT 0;
        RAISE NOTICE 'Added view_count column to products table';
    END IF;
END $$;

-- 4. Update existing products to have a category_id (assign to first available category)
DO $$
DECLARE
    first_category_id UUID;
BEGIN
    -- Get first category ID
    SELECT id INTO first_category_id FROM categories LIMIT 1;
    
    IF first_category_id IS NOT NULL THEN
        -- Update products without category_id
        UPDATE products 
        SET category_id = first_category_id 
        WHERE category_id IS NULL;
        
        RAISE NOTICE 'Updated products without category_id to use category: %', first_category_id;
    END IF;
END $$;

-- 5. Show final table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 6. Show sample data
SELECT id, name_tr, price, stock, category_id, is_available 
FROM products 
LIMIT 5;

COMMIT;
