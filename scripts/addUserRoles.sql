-- <PERSON><PERSON><PERSON><PERSON><PERSON> Rolleri Sistemi

-- 1. Users tablosuna role kolonu ekle (<PERSON><PERSON><PERSON> yoksa)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role') THEN
        ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user' CHECK (role IN ('user', 'moderator', 'finance_manager', 'admin'));
    END IF;
END $$;

-- 2. Users tablosuna role_permissions kolonu ekle (JSON array)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'role_permissions') THEN
        ALTER TABLE users ADD COLUMN role_permissions TEXT[] DEFAULT ARRAY['read_products'];
    END IF;
END $$;

-- 3. Users tablosuna created_by kolonu ekle (kim tarafından oluşturuldu)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'created_by') THEN
        ALTER TABLE users ADD COLUMN created_by TEXT REFERENCES users(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 4. Users tablosuna last_login kolonu ekle
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login') THEN
        ALTER TABLE users ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- 5. Admin Activity Log tablosu oluştur
CREATE TABLE IF NOT EXISTS admin_activity_log (
  id SERIAL PRIMARY KEY,
  admin_id TEXT REFERENCES users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  target_type TEXT NOT NULL, -- 'product', 'user', 'order', etc.
  target_id TEXT,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Role Permissions tablosu oluştur
CREATE TABLE IF NOT EXISTS role_permissions (
  id SERIAL PRIMARY KEY,
  role TEXT NOT NULL,
  permission TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role, permission)
);

-- 7. Default role permissions ekle
INSERT INTO role_permissions (role, permission, description) VALUES
-- User permissions
('user', 'read_products', 'Ürünleri görüntüleyebilir'),
('user', 'create_orders', 'Sipariş oluşturabilir'),
('user', 'manage_own_cart', 'Kendi sepetini yönetebilir'),
('user', 'manage_own_wishlist', 'Kendi favorilerini yönetebilir'),
('user', 'join_giveaways', 'Çekilişlere katılabilir'),
('user', 'bid_auctions', 'Açık artırmalara teklif verebilir'),
('user', 'manage_own_wallet', 'Kendi cüzdanını yönetebilir'),

-- Moderator permissions (user + extra)
('moderator', 'read_products', 'Ürünleri görüntüleyebilir'),
('moderator', 'create_orders', 'Sipariş oluşturabilir'),
('moderator', 'manage_own_cart', 'Kendi sepetini yönetebilir'),
('moderator', 'manage_own_wishlist', 'Kendi favorilerini yönetebilir'),
('moderator', 'join_giveaways', 'Çekilişlere katılabilir'),
('moderator', 'bid_auctions', 'Açık artırmalara teklif verebilir'),
('moderator', 'manage_own_wallet', 'Kendi cüzdanını yönetebilir'),
('moderator', 'moderate_reviews', 'Yorumları moderasyona tabi tutabilir'),
('moderator', 'view_user_reports', 'Kullanıcı raporlarını görüntüleyebilir'),

-- Finance Manager permissions (moderator + extra)
('finance_manager', 'read_products', 'Ürünleri görüntüleyebilir'),
('finance_manager', 'create_orders', 'Sipariş oluşturabilir'),
('finance_manager', 'manage_own_cart', 'Kendi sepetini yönetebilir'),
('finance_manager', 'manage_own_wishlist', 'Kendi favorilerini yönetebilir'),
('finance_manager', 'join_giveaways', 'Çekilişlere katılabilir'),
('finance_manager', 'bid_auctions', 'Açık artırmalara teklif verebilir'),
('finance_manager', 'manage_own_wallet', 'Kendi cüzdanını yönetebilir'),
('finance_manager', 'moderate_reviews', 'Yorumları moderasyona tabi tutabilir'),
('finance_manager', 'view_user_reports', 'Kullanıcı raporlarını görüntüleyebilir'),
('finance_manager', 'manage_wallet_requests', 'Cüzdan yükleme taleplerini yönetebilir'),
('finance_manager', 'view_financial_reports', 'Finansal raporları görüntüleyebilir'),
('finance_manager', 'manage_campaigns', 'Kampanyaları yönetebilir'),

-- Admin permissions (all)
('admin', 'read_products', 'Ürünleri görüntüleyebilir'),
('admin', 'create_products', 'Ürün oluşturabilir'),
('admin', 'update_products', 'Ürünleri güncelleyebilir'),
('admin', 'delete_products', 'Ürünleri silebilir'),
('admin', 'create_orders', 'Sipariş oluşturabilir'),
('admin', 'manage_all_orders', 'Tüm siparişleri yönetebilir'),
('admin', 'manage_own_cart', 'Kendi sepetini yönetebilir'),
('admin', 'manage_own_wishlist', 'Kendi favorilerini yönetebilir'),
('admin', 'join_giveaways', 'Çekilişlere katılabilir'),
('admin', 'manage_giveaways', 'Çekilişleri yönetebilir'),
('admin', 'bid_auctions', 'Açık artırmalara teklif verebilir'),
('admin', 'manage_auctions', 'Açık artırmaları yönetebilir'),
('admin', 'manage_own_wallet', 'Kendi cüzdanını yönetebilir'),
('admin', 'manage_all_wallets', 'Tüm cüzdanları yönetebilir'),
('admin', 'moderate_reviews', 'Yorumları moderasyona tabi tutabilir'),
('admin', 'view_user_reports', 'Kullanıcı raporlarını görüntüleyebilir'),
('admin', 'manage_wallet_requests', 'Cüzdan yükleme taleplerini yönetebilir'),
('admin', 'view_financial_reports', 'Finansal raporları görüntüleyebilir'),
('admin', 'manage_campaigns', 'Kampanyaları yönetebilir'),
('admin', 'manage_users', 'Kullanıcıları yönetebilir'),
('admin', 'manage_categories', 'Kategorileri yönetebilir'),
('admin', 'view_admin_dashboard', 'Admin dashboard\'ına erişebilir'),
('admin', 'manage_system_settings', 'Sistem ayarlarını yönetebilir')
ON CONFLICT (role, permission) DO NOTHING;

-- 8. İndeksler
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_admin ON admin_activity_log(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created ON admin_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role);

-- 9. RLS Politikaları
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;

-- Admin activity log sadece adminler görebilir
CREATE POLICY "admin_activity_log_policy" ON admin_activity_log FOR ALL USING (true) WITH CHECK (true);

-- Role permissions herkes okuyabilir
CREATE POLICY "role_permissions_policy" ON role_permissions FOR SELECT USING (true);

-- 10. İlk admin kullanıcısı oluştur (isteğe bağlı - manuel olarak yapılabilir)
-- Bu kısmı manuel olarak çalıştırın ve kendi Clerk ID'nizi kullanın
/*
UPDATE users 
SET role = 'admin', 
    role_permissions = ARRAY[
        'read_products', 'create_products', 'update_products', 'delete_products',
        'manage_all_orders', 'manage_giveaways', 'manage_auctions', 
        'manage_all_wallets', 'manage_users', 'manage_categories',
        'view_admin_dashboard', 'manage_system_settings'
    ]
WHERE clerk_id = 'YOUR_CLERK_ID_HERE';
*/
