const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://owlinhiyrbfpbgbrvjge.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im93bGluaGl5cmJmcGJnYnJ2amdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjk1MjczNiwiZXhwIjoyMDY4NTI4NzM2fQ.isUZbZpPUT7hni9nbrK9fg_GY-V9rhs9zL6XN1EFxAo';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Gerçek veriler
const realGroups = [
  { id: 'react-native', name: 'r/ReactNative', image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop&crop=center' },
  { id: 'javascript', name: 'r/JavaScript', image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop&crop=center' },
  { id: 'webdev', name: 'r/WebDev', image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=100&h=100&fit=crop&crop=center' },
  { id: 'programming', name: 'r/Programming', image: 'https://images.unsplash.com/photo-1515879218367-8466d910aaa4?w=100&h=100&fit=crop&crop=center' },
  { id: 'frontend', name: 'r/Frontend', image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=100&h=100&fit=crop&crop=center' },
  { id: 'mobile', name: 'r/MobileDev', image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=100&h=100&fit=crop&crop=center' },
  { id: 'ai', name: 'r/ArtificialIntelligence', image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=center' },
  { id: 'startup', name: 'r/Startups', image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=100&h=100&fit=crop&crop=center' }
];

const realUsers = [
  { id: 'user-dev1', name: 'ReactNativePro', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev2', name: 'JSMaster', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev3', name: 'WebDevGuru', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev4', name: 'CleanCoder', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev5', name: 'FrontendNinja', image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev6', name: 'MobileExpert', image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev7', name: 'AIEnthusiast', image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev8', name: 'StartupFounder', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev9', name: 'PerformanceGuru', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev10', name: 'AsyncMaster', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev11', name: 'NextJSPro', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev12', name: 'PatternMaster', image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev13', name: 'CSSWizard', image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev14', name: 'OfflineFirst', image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-dev15', name: 'MLDeveloper', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  // Comment users
  { id: 'user-comm1', name: 'TechReviewer', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm2', name: 'CodeCritic', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm3', name: 'DateTimeExpert', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm4', name: 'PatternFan', image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm5', name: 'GridMaster', image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm6', name: 'CleanCodeAdvocate', image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm7', name: 'TeamPlayer', image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm8', name: 'ZustandFan', image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm9', name: 'FrameworkComparator', image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face' },
  { id: 'user-comm10', name: 'AIFuturist', image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face' }
];

const realPosts = [
  {
    id: 'post-rn-1',
    title: 'React Native 0.76 Released with New Architecture Improvements',
    description: 'The latest React Native release brings significant performance improvements and better developer experience. Key features include improved Hermes engine, better debugging tools, and enhanced TypeScript support.',
    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=600&fit=crop',
    group_id: 'react-native',
    user_id: 'user-dev1',
    upvotes: 245,
    nr_of_comments: 32,
    created_at: '2025-01-18T10:30:00Z'
  },
  {
    id: 'post-js-1',
    title: 'JavaScript ES2025 Features You Should Know',
    description: 'Exploring the latest JavaScript features including temporal API, pattern matching, and improved async/await syntax. These features will change how we write JavaScript.',
    image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=800&h=600&fit=crop',
    group_id: 'javascript',
    user_id: 'user-dev2',
    upvotes: 189,
    nr_of_comments: 28,
    created_at: '2025-01-18T09:15:00Z'
  },
  {
    id: 'post-web-1',
    title: 'Building Responsive Websites with CSS Grid and Flexbox',
    description: 'A comprehensive guide to modern CSS layout techniques. Learn how to create responsive designs that work across all devices using CSS Grid and Flexbox.',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop',
    group_id: 'webdev',
    user_id: 'user-dev3',
    upvotes: 156,
    nr_of_comments: 19,
    created_at: '2025-01-18T08:45:00Z'
  },
  {
    id: 'post-prog-1',
    title: 'Clean Code Principles Every Developer Should Follow',
    description: 'Writing maintainable code is crucial for long-term project success. Here are the essential clean code principles that will make your code more readable and maintainable.',
    image: null,
    group_id: 'programming',
    user_id: 'user-dev4',
    upvotes: 312,
    nr_of_comments: 45,
    created_at: '2025-01-18T07:20:00Z'
  },
  {
    id: 'post-front-1',
    title: 'State Management in React: Redux vs Zustand vs Context',
    description: 'Comparing different state management solutions for React applications. When to use each approach and their pros and cons.',
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',
    group_id: 'frontend',
    user_id: 'user-dev5',
    upvotes: 198,
    nr_of_comments: 34,
    created_at: '2025-01-17T16:30:00Z'
  },
  {
    id: 'post-mobile-1',
    title: 'Flutter vs React Native: 2025 Comparison',
    description: 'An updated comparison of the two leading cross-platform mobile development frameworks. Performance, developer experience, and ecosystem analysis.',
    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop',
    group_id: 'mobile',
    user_id: 'user-dev6',
    upvotes: 267,
    nr_of_comments: 52,
    created_at: '2025-01-17T15:10:00Z'
  },
  {
    id: 'post-ai-1',
    title: 'Building AI-Powered Apps with OpenAI GPT-4',
    description: 'Learn how to integrate AI capabilities into your applications using OpenAI\'s latest models. Practical examples and best practices included.',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
    group_id: 'ai',
    user_id: 'user-dev7',
    upvotes: 423,
    nr_of_comments: 67,
    created_at: '2025-01-17T14:25:00Z'
  },
  {
    id: 'post-startup-1',
    title: 'From Idea to MVP: A Developer\'s Guide to Startups',
    description: 'Essential steps for developers looking to build their own startup. Technical decisions, team building, and product development strategies.',
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=600&fit=crop',
    group_id: 'startup',
    user_id: 'user-dev8',
    upvotes: 178,
    nr_of_comments: 29,
    created_at: '2025-01-17T13:40:00Z'
  },
  {
    id: 'post-rn-2',
    title: 'Optimizing React Native Performance: Tips and Tricks',
    description: 'Performance optimization techniques for React Native apps. Memory management, rendering optimization, and bundle size reduction strategies.',
    image: null,
    group_id: 'react-native',
    user_id: 'user-dev9',
    upvotes: 134,
    nr_of_comments: 22,
    created_at: '2025-01-17T12:15:00Z'
  },
  {
    id: 'post-js-2',
    title: 'Async/Await vs Promises: When to Use What',
    description: 'Understanding the differences between async/await and promises in JavaScript. Best practices for handling asynchronous operations.',
    image: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=600&fit=crop',
    group_id: 'javascript',
    user_id: 'user-dev10',
    upvotes: 89,
    nr_of_comments: 15,
    created_at: '2025-01-17T11:30:00Z'
  },
  {
    id: 'post-web-2',
    title: 'Next.js 15: What\'s New and How to Migrate',
    description: 'Exploring the new features in Next.js 15 including improved performance, better developer experience, and new deployment options.',
    image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=600&fit=crop',
    group_id: 'webdev',
    user_id: 'user-dev11',
    upvotes: 201,
    nr_of_comments: 38,
    created_at: '2025-01-17T10:45:00Z'
  },
  {
    id: 'post-prog-2',
    title: 'Design Patterns Every Developer Should Know',
    description: 'Essential design patterns for software development. Singleton, Factory, Observer, and more with practical examples.',
    image: null,
    group_id: 'programming',
    user_id: 'user-dev12',
    upvotes: 278,
    nr_of_comments: 41,
    created_at: '2025-01-17T09:20:00Z'
  },
  {
    id: 'post-front-2',
    title: 'CSS-in-JS vs Traditional CSS: 2025 Perspective',
    description: 'Comparing modern CSS approaches. Styled-components, Emotion, CSS Modules, and traditional CSS - which one to choose?',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
    group_id: 'frontend',
    user_id: 'user-dev13',
    upvotes: 145,
    nr_of_comments: 26,
    created_at: '2025-01-17T08:35:00Z'
  },
  {
    id: 'post-mobile-2',
    title: 'Building Offline-First Mobile Apps',
    description: 'Strategies for creating mobile applications that work seamlessly offline. Data synchronization, caching, and user experience considerations.',
    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',
    group_id: 'mobile',
    user_id: 'user-dev14',
    upvotes: 167,
    nr_of_comments: 31,
    created_at: '2025-01-16T17:50:00Z'
  },
  {
    id: 'post-ai-2',
    title: 'Machine Learning for Web Developers',
    description: 'Getting started with machine learning as a web developer. TensorFlow.js, practical examples, and real-world applications.',
    image: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=800&h=600&fit=crop',
    group_id: 'ai',
    user_id: 'user-dev15',
    upvotes: 356,
    nr_of_comments: 48,
    created_at: '2025-01-16T16:25:00Z'
  }
];

const realComments = [
  // Post 1 comments
  { id: 'comment-rn-1-1', post_id: 'post-rn-1', user_id: 'user-comm1', parent_id: null, comment: 'Great overview! The new architecture improvements are really impressive.', upvotes: 12, created_at: '2025-01-18T11:00:00Z' },
  { id: 'comment-rn-1-2', post_id: 'post-rn-1', user_id: 'user-comm2', parent_id: 'comment-rn-1-1', comment: 'Totally agree! The performance boost is noticeable.', upvotes: 8, created_at: '2025-01-18T11:15:00Z' },
  
  // Post 2 comments
  { id: 'comment-js-1-1', post_id: 'post-js-1', user_id: 'user-comm3', parent_id: null, comment: 'The temporal API is going to be a game changer for date handling.', upvotes: 15, created_at: '2025-01-18T09:45:00Z' },
  { id: 'comment-js-1-2', post_id: 'post-js-1', user_id: 'user-comm4', parent_id: null, comment: 'Pattern matching will make code so much cleaner!', upvotes: 9, created_at: '2025-01-18T10:20:00Z' },
  
  // Post 3 comments
  { id: 'comment-web-1-1', post_id: 'post-web-1', user_id: 'user-comm5', parent_id: null, comment: 'CSS Grid has revolutionized how I approach layouts.', upvotes: 7, created_at: '2025-01-18T09:00:00Z' },
  
  // Post 4 comments
  { id: 'comment-prog-1-1', post_id: 'post-prog-1', user_id: 'user-comm6', parent_id: null, comment: 'Clean code is so important for team collaboration.', upvotes: 18, created_at: '2025-01-18T07:45:00Z' },
  { id: 'comment-prog-1-2', post_id: 'post-prog-1', user_id: 'user-comm7', parent_id: 'comment-prog-1-1', comment: 'Absolutely! It saves so much time in the long run.', upvotes: 11, created_at: '2025-01-18T08:00:00Z' },
  
  // Post 5 comments
  { id: 'comment-front-1-1', post_id: 'post-front-1', user_id: 'user-comm8', parent_id: null, comment: 'Zustand is my go-to for smaller projects now.', upvotes: 14, created_at: '2025-01-17T17:00:00Z' },
  
  // More comments for other posts...
  { id: 'comment-mobile-1-1', post_id: 'post-mobile-1', user_id: 'user-comm9', parent_id: null, comment: 'Both frameworks have their strengths. Great comparison!', upvotes: 16, created_at: '2025-01-17T15:30:00Z' },
  { id: 'comment-ai-1-1', post_id: 'post-ai-1', user_id: 'user-comm10', parent_id: null, comment: 'AI integration is the future of app development.', upvotes: 22, created_at: '2025-01-17T14:50:00Z' }
];

async function seedRealData() {
  try {
    console.log('🌱 Starting real data seeding...');

    // Clear existing data
    console.log('🗑️ Clearing existing data...');
    await supabase.from('comments').delete().neq('id', '');
    await supabase.from('posts').delete().neq('id', '');
    await supabase.from('groups').delete().neq('id', '');
    await supabase.from('users').delete().neq('id', '');

    // Insert users first
    console.log('👥 Inserting users...');
    const { data: insertedUsers, error: usersError } = await supabase
      .from('users')
      .insert(realUsers)
      .select();

    if (usersError) {
      console.error('Error inserting users:', usersError);
      return;
    }
    console.log(`✅ Inserted ${insertedUsers.length} users`);

    // Insert groups
    console.log('📁 Inserting groups...');
    const { data: insertedGroups, error: groupsError } = await supabase
      .from('groups')
      .insert(realGroups)
      .select();

    if (groupsError) {
      console.error('Error inserting groups:', groupsError);
      return;
    }
    console.log(`✅ Inserted ${insertedGroups.length} groups`);

    // Insert posts
    console.log('📝 Inserting posts...');
    const { data: insertedPosts, error: postsError } = await supabase
      .from('posts')
      .insert(realPosts)
      .select();

    if (postsError) {
      console.error('Error inserting posts:', postsError);
      return;
    }
    console.log(`✅ Inserted ${insertedPosts.length} posts`);

    // Insert comments
    console.log('💬 Inserting comments...');
    const { data: insertedComments, error: commentsError } = await supabase
      .from('comments')
      .insert(realComments)
      .select();

    if (commentsError) {
      console.error('Error inserting comments:', commentsError);
      return;
    }
    console.log(`✅ Inserted ${insertedComments.length} comments`);

    console.log('🎉 Real data seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding real data:', error);
  }
}

seedRealData();
