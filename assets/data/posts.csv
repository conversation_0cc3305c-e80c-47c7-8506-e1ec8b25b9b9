id,title,created_at,upvotes,nr_of_comments,description,image,group_id,user_id
post-1,"Why React Native is the best?","2025-02-12T08:30:00Z",120,15,"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/3.jpg,group-1,user-1
post-2,"Exploring Next.js Features in Depth","2025-02-11T14:22:00Z",200,30,"Next.js brings powerful features for React developers, such as server-side rendering, static generation, and more. In this post, I’ll dive deep into each of these. Feel free to share your opinion, it's always interesting to keep the discussion going!",,group-2,user-2
post-3,"How to Build a Portfolio Website with HTML and CSS","2025-02-10T09:10:00Z",85,10,,https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/8.jpg,group-3,user-3
post-4,"Top JavaScript Frameworks to Learn in 2025","2025-02-09T18:45:00Z",50,5,"In 2025, JavaScript frameworks continue to evolve. Let’s explore the top frameworks that will shape the development world this year.",,group-4,user-4
post-5,"Understanding the Basics of Machine Learning","2025-02-08T15:00:00Z",175,20,,https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/6.jpg,group-5,user-5
post-6,"How to Get Started with TypeScript in 2025","2025-02-07T16:10:00Z",95,12,,https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/1.jpg,group-6,user-6
post-7,"A Deep Dive into React Hooks","2025-02-06T11:00:00Z",150,25,"React Hooks allow you to use state and other features without writing a class. In this post, I will walk you through various hooks in detail.",,group-7,user-7
post-8,"Exploring the World of Serverless Architecture","2025-02-05T17:30:00Z",220,40,"Serverless architecture is revolutionizing cloud computing. Learn about the benefits, challenges, and how to get started.",,group-8,user-8
post-9,"Best Practices for Web Accessibility","2025-02-04T14:40:00Z",90,10,"Web accessibility is crucial for making websites usable for everyone. Let’s explore the best practices for creating accessible websites.",,group-9,user-9
post-10,"The Future of Progressive Web Apps","2025-02-03T19:20:00Z",170,22,,https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/5.jpeg,group-10,user-10