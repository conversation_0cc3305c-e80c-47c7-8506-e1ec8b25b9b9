{"name": "redditclone", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "gen-types": "npx supabase gen types typescript --project-id owlinhiyrbfpbgbrvjge > src/types/database.types.ts", "seed-ecommerce": "node scripts/seedEcommerce.js"}, "dependencies": {"@clerk/clerk-expo": "^2.7.8", "@clerk/types": "^4.46.1", "@dev-plugins/react-query": "^0.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.66.11", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "expo": "~52.0.37", "expo-constants": "~17.0.7", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.17", "expo-secure-store": "~14.0.1", "expo-status-bar": "~2.0.1", "jotai": "^2.12.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}