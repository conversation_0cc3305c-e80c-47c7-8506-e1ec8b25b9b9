import { useEffect } from 'react';
import { useUser as useClerkUser } from '@clerk/clerk-expo';
import { useSupabase } from '../lib/supabase';

export const useUser = () => {
  const { user, isLoaded } = useClerkUser();
  const supabase = useSupabase();

  useEffect(() => {
    const syncUser = async () => {
      if (!isLoaded || !user) return;

      try {
        // Kullanıcının Supabase'de var olup olmadığını kontrol et
        const { data: existingUser, error: fetchError } = await supabase
          .from('users')
          .select('id')
          .eq('clerk_id', user.id)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') {
          console.error('User fetch error:', fetchError);
          return;
        }

        // Eğer kullanıcı yoksa, oluştur
        if (!existingUser) {
          const { error: insertError } = await supabase
            .from('users')
            .insert({
              id: user.id, // Clerk ID'sini primary key olarak kullan
              clerk_id: user.id,
              name: user.fullName || user.firstName || 'Kullanıcı',
              email: user.primaryEmailAddress?.emailAddress,
              image: user.imageUrl,
            });

          if (insertError) {
            console.error('User insert error:', insertError);
          } else {
            console.log('User synced to Supabase');
          }
        }
      } catch (error) {
        console.error('User sync error:', error);
      }
    };

    syncUser();
  }, [user, isLoaded, supabase]);

  return { user, isLoaded };
};
