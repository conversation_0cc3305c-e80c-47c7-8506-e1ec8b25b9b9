import { useMemo } from 'react';
import { useSupabase } from '../lib/supabase';
import { ServiceFactory } from '../services';

/**
 * Custom hook to access all services
 */
export const useServices = () => {
  const supabase = useSupabase();

  const services = useMemo(() => {
    return ServiceFactory.getInstance(supabase);
  }, [supabase]);

  return {
    categoryService: services.categoryService,
    productService: services.productService,
  };
};
