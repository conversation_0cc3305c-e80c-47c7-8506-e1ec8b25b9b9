export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      analytics: {
        Row: {
          ab_test_data: Json | null
          conversion_rate: number | null
          conversions: number | null
          countries: string[] | null
          coupon_usage: number | null
          created_at: string | null
          currency: string | null
          funnel_data: Json | null
          id: string
          loyalty_tiers: string[] | null
          participants: number | null
          related_auction_id: string | null
          related_campaign_id: string | null
          related_category_id: string | null
          related_giveaway_id: string | null
          related_order_id: string | null
          related_product_id: string | null
          related_user_id: string | null
          roi: number | null
          social_shares: number | null
          time_range_end: string
          time_range_start: string
          total_revenue: number | null
          type: string
          user_roles: string[] | null
        }
        Insert: {
          ab_test_data?: Json | null
          conversion_rate?: number | null
          conversions?: number | null
          countries?: string[] | null
          coupon_usage?: number | null
          created_at?: string | null
          currency?: string | null
          funnel_data?: Json | null
          id: string
          loyalty_tiers?: string[] | null
          participants?: number | null
          related_auction_id?: string | null
          related_campaign_id?: string | null
          related_category_id?: string | null
          related_giveaway_id?: string | null
          related_order_id?: string | null
          related_product_id?: string | null
          related_user_id?: string | null
          roi?: number | null
          social_shares?: number | null
          time_range_end: string
          time_range_start: string
          total_revenue?: number | null
          type: string
          user_roles?: string[] | null
        }
        Update: {
          ab_test_data?: Json | null
          conversion_rate?: number | null
          conversions?: number | null
          countries?: string[] | null
          coupon_usage?: number | null
          created_at?: string | null
          currency?: string | null
          funnel_data?: Json | null
          id?: string
          loyalty_tiers?: string[] | null
          participants?: number | null
          related_auction_id?: string | null
          related_campaign_id?: string | null
          related_category_id?: string | null
          related_giveaway_id?: string | null
          related_order_id?: string | null
          related_product_id?: string | null
          related_user_id?: string | null
          roi?: number | null
          social_shares?: number | null
          time_range_end?: string
          time_range_start?: string
          total_revenue?: number | null
          type?: string
          user_roles?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_related_auction_id_fkey"
            columns: ["related_auction_id"]
            isOneToOne: false
            referencedRelation: "auctions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_campaign_id_fkey"
            columns: ["related_campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_category_id_fkey"
            columns: ["related_category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_giveaway_id_fkey"
            columns: ["related_giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_order_id_fkey"
            columns: ["related_order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_product_id_fkey"
            columns: ["related_product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_related_user_id_fkey"
            columns: ["related_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      auction_bidders: {
        Row: {
          auction_id: string | null
          bidder_id: string | null
          id: number
          joined_at: string | null
        }
        Insert: {
          auction_id?: string | null
          bidder_id?: string | null
          id?: number
          joined_at?: string | null
        }
        Update: {
          auction_id?: string | null
          bidder_id?: string | null
          id?: number
          joined_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "auction_bidders_auction_id_fkey"
            columns: ["auction_id"]
            isOneToOne: false
            referencedRelation: "auctions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "auction_bidders_bidder_id_fkey"
            columns: ["bidder_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      auction_bids: {
        Row: {
          auction_id: string | null
          bid_amount: number
          bid_time: string | null
          bidder_id: string | null
          id: number
          is_winning_bid: boolean | null
        }
        Insert: {
          auction_id?: string | null
          bid_amount: number
          bid_time?: string | null
          bidder_id?: string | null
          id?: number
          is_winning_bid?: boolean | null
        }
        Update: {
          auction_id?: string | null
          bid_amount?: number
          bid_time?: string | null
          bidder_id?: string | null
          id?: number
          is_winning_bid?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "auction_bids_auction_id_fkey"
            columns: ["auction_id"]
            isOneToOne: false
            referencedRelation: "auctions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "auction_bids_bidder_id_fkey"
            columns: ["bidder_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      auctions: {
        Row: {
          bid_increment_amount: number
          campaign_id: string | null
          created_at: string | null
          currency: string
          current_bid: number | null
          description_en: string | null
          description_tr: string | null
          end_time: string
          id: string
          image: string | null
          product_id: string | null
          start_time: string
          starting_bid: number
          status: string
          updated_at: string | null
          winner_id: string | null
        }
        Insert: {
          bid_increment_amount?: number
          campaign_id?: string | null
          created_at?: string | null
          currency?: string
          current_bid?: number | null
          description_en?: string | null
          description_tr?: string | null
          end_time: string
          id: string
          image?: string | null
          product_id?: string | null
          start_time: string
          starting_bid: number
          status?: string
          updated_at?: string | null
          winner_id?: string | null
        }
        Update: {
          bid_increment_amount?: number
          campaign_id?: string | null
          created_at?: string | null
          currency?: string
          current_bid?: number | null
          description_en?: string | null
          description_tr?: string | null
          end_time?: string
          id?: string
          image?: string | null
          product_id?: string | null
          start_time?: string
          starting_bid?: number
          status?: string
          updated_at?: string | null
          winner_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "auctions_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "auctions_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "auctions_winner_id_fkey"
            columns: ["winner_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_categories: {
        Row: {
          campaign_id: string | null
          category_id: string | null
          created_at: string | null
          id: number
        }
        Insert: {
          campaign_id?: string | null
          category_id?: string | null
          created_at?: string | null
          id?: number
        }
        Update: {
          campaign_id?: string | null
          category_id?: string | null
          created_at?: string | null
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "campaign_categories_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_products: {
        Row: {
          campaign_id: string | null
          created_at: string | null
          id: number
          product_id: string | null
        }
        Insert: {
          campaign_id?: string | null
          created_at?: string | null
          id?: number
          product_id?: string | null
        }
        Update: {
          campaign_id?: string | null
          created_at?: string | null
          id?: number
          product_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_products_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_users: {
        Row: {
          campaign_id: string | null
          created_at: string | null
          id: number
          user_id: string | null
        }
        Insert: {
          campaign_id?: string | null
          created_at?: string | null
          id?: number
          user_id?: string | null
        }
        Update: {
          campaign_id?: string | null
          created_at?: string | null
          id?: number
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_users_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      cart_items: {
        Row: {
          created_at: string | null
          id: number
          product_id: string | null
          quantity: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          quantity: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          quantity?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "cart_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description_en: string | null
          description_tr: string | null
          id: string
          image: string | null
          name_en: string | null
          name_tr: string
        }
        Insert: {
          created_at?: string | null
          description_en?: string | null
          description_tr?: string | null
          id: string
          image?: string | null
          name_en?: string | null
          name_tr: string
        }
        Update: {
          created_at?: string | null
          description_en?: string | null
          description_tr?: string | null
          id?: string
          image?: string | null
          name_en?: string | null
          name_tr?: string
        }
        Relationships: []
      }
      coupon_usage: {
        Row: {
          campaign_id: string | null
          discount_applied: number
          id: number
          order_id: string | null
          used_at: string | null
          user_id: string | null
        }
        Insert: {
          campaign_id?: string | null
          discount_applied: number
          id?: number
          order_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Update: {
          campaign_id?: string | null
          discount_applied?: number
          id?: number
          order_id?: string | null
          used_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "coupon_usage_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coupon_usage_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coupon_usage_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      giveaway_participants: {
        Row: {
          giveaway_id: string | null
          id: number
          joined_at: string | null
          user_id: string | null
        }
        Insert: {
          giveaway_id?: string | null
          id?: number
          joined_at?: string | null
          user_id?: string | null
        }
        Update: {
          giveaway_id?: string | null
          id?: number
          joined_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "giveaway_participants_giveaway_id_fkey"
            columns: ["giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      giveaway_prizes: {
        Row: {
          created_at: string | null
          currency: string | null
          description_en: string | null
          description_tr: string | null
          giveaway_id: string | null
          id: number
          image: string | null
          prize_type: string
          product_id: string | null
          rank: number
          title_en: string | null
          title_tr: string
          value: number | null
        }
        Insert: {
          created_at?: string | null
          currency?: string | null
          description_en?: string | null
          description_tr?: string | null
          giveaway_id?: string | null
          id?: number
          image?: string | null
          prize_type?: string
          product_id?: string | null
          rank: number
          title_en?: string | null
          title_tr: string
          value?: number | null
        }
        Update: {
          created_at?: string | null
          currency?: string | null
          description_en?: string | null
          description_tr?: string | null
          giveaway_id?: string | null
          id?: number
          image?: string | null
          prize_type?: string
          product_id?: string | null
          rank?: number
          title_en?: string | null
          title_tr?: string
          value?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "giveaway_prizes_giveaway_id_fkey"
            columns: ["giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_prizes_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      giveaway_tickets: {
        Row: {
          chosen_digit_count: number
          giveaway_id: string | null
          id: number
          order_id: string | null
          purchased_at: string | null
          status: string
          ticket_number: string
          user_id: string | null
        }
        Insert: {
          chosen_digit_count: number
          giveaway_id?: string | null
          id?: number
          order_id?: string | null
          purchased_at?: string | null
          status?: string
          ticket_number: string
          user_id?: string | null
        }
        Update: {
          chosen_digit_count?: number
          giveaway_id?: string | null
          id?: number
          order_id?: string | null
          purchased_at?: string | null
          status?: string
          ticket_number?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "giveaway_tickets_giveaway_id_fkey"
            columns: ["giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_tickets_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_tickets_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      giveaway_winners: {
        Row: {
          announced_at: string | null
          giveaway_id: string | null
          id: number
          prize_id: number | null
          rank: number
          ticket_number: string
          user_id: string | null
        }
        Insert: {
          announced_at?: string | null
          giveaway_id?: string | null
          id?: number
          prize_id?: number | null
          rank: number
          ticket_number: string
          user_id?: string | null
        }
        Update: {
          announced_at?: string | null
          giveaway_id?: string | null
          id?: number
          prize_id?: number | null
          rank?: number
          ticket_number?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "giveaway_winners_giveaway_id_fkey"
            columns: ["giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_winners_prize_id_fkey"
            columns: ["prize_id"]
            isOneToOne: false
            referencedRelation: "giveaway_prizes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "giveaway_winners_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      giveaways: {
        Row: {
          campaign_id: string | null
          created_at: string | null
          currency: string
          description_en: string | null
          description_tr: string | null
          draw_date: string | null
          end_date: string
          id: string
          image: string | null
          max_tickets_per_user: number | null
          numbers_per_card: number | null
          rules_en: string | null
          rules_tr: string | null
          start_date: string
          status: string
          ticket_digit_length: number | null
          ticket_price: number
          ticket_sale_percentage_for_draw: number | null
          tickets_sold: number | null
          title_en: string | null
          title_tr: string
          total_tickets: number
          updated_at: string | null
          winning_numbers: string[] | null
        }
        Insert: {
          campaign_id?: string | null
          created_at?: string | null
          currency?: string
          description_en?: string | null
          description_tr?: string | null
          draw_date?: string | null
          end_date: string
          id: string
          image?: string | null
          max_tickets_per_user?: number | null
          numbers_per_card?: number | null
          rules_en?: string | null
          rules_tr?: string | null
          start_date: string
          status?: string
          ticket_digit_length?: number | null
          ticket_price: number
          ticket_sale_percentage_for_draw?: number | null
          tickets_sold?: number | null
          title_en?: string | null
          title_tr: string
          total_tickets: number
          updated_at?: string | null
          winning_numbers?: string[] | null
        }
        Update: {
          campaign_id?: string | null
          created_at?: string | null
          currency?: string
          description_en?: string | null
          description_tr?: string | null
          draw_date?: string | null
          end_date?: string
          id?: string
          image?: string | null
          max_tickets_per_user?: number | null
          numbers_per_card?: number | null
          rules_en?: string | null
          rules_tr?: string | null
          start_date?: string
          status?: string
          ticket_digit_length?: number | null
          ticket_price?: number
          ticket_sale_percentage_for_draw?: number | null
          tickets_sold?: number | null
          title_en?: string | null
          title_tr?: string
          total_tickets?: number
          updated_at?: string | null
          winning_numbers?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "giveaways_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string | null
          id: string
          is_read: boolean | null
          message_en: string | null
          message_tr: string
          related_auction_id: string | null
          related_campaign_id: string | null
          related_giveaway_id: string | null
          related_order_id: string | null
          sent_at: string | null
          title_en: string | null
          title_tr: string
          type: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          is_read?: boolean | null
          message_en?: string | null
          message_tr: string
          related_auction_id?: string | null
          related_campaign_id?: string | null
          related_giveaway_id?: string | null
          related_order_id?: string | null
          sent_at?: string | null
          title_en?: string | null
          title_tr: string
          type: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_read?: boolean | null
          message_en?: string | null
          message_tr?: string
          related_auction_id?: string | null
          related_campaign_id?: string | null
          related_giveaway_id?: string | null
          related_order_id?: string | null
          sent_at?: string | null
          title_en?: string | null
          title_tr?: string
          type?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_related_auction_id_fkey"
            columns: ["related_auction_id"]
            isOneToOne: false
            referencedRelation: "auctions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_related_campaign_id_fkey"
            columns: ["related_campaign_id"]
            isOneToOne: false
            referencedRelation: "sales_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_related_giveaway_id_fkey"
            columns: ["related_giveaway_id"]
            isOneToOne: false
            referencedRelation: "giveaways"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_related_order_id_fkey"
            columns: ["related_order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string | null
          currency: string
          id: number
          order_id: string | null
          price: number
          product_id: string | null
          quantity: number
        }
        Insert: {
          created_at?: string | null
          currency?: string
          id?: number
          order_id?: string | null
          price: number
          product_id?: string | null
          quantity: number
        }
        Update: {
          created_at?: string | null
          currency?: string
          id?: number
          order_id?: string | null
          price?: number
          product_id?: string | null
          quantity?: number
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          campaign_code: string | null
          created_at: string | null
          currency: string
          customer_id: string | null
          delivery_address: Json | null
          id: string
          order_number: string
          order_status: string
          payment_intent_id: string | null
          payment_status: string
          source: string
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          campaign_code?: string | null
          created_at?: string | null
          currency?: string
          customer_id?: string | null
          delivery_address?: Json | null
          id: string
          order_number: string
          order_status?: string
          payment_intent_id?: string | null
          payment_status?: string
          source?: string
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          campaign_code?: string | null
          created_at?: string | null
          currency?: string
          customer_id?: string | null
          delivery_address?: Json | null
          id?: string
          order_number?: string
          order_status?: string
          payment_intent_id?: string | null
          payment_status?: string
          source?: string
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      product_categories: {
        Row: {
          category_id: string | null
          created_at: string | null
          id: number
          product_id: string | null
        }
        Insert: {
          category_id?: string | null
          created_at?: string | null
          id?: number
          product_id?: string | null
        }
        Update: {
          category_id?: string | null
          created_at?: string | null
          id?: number
          product_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_categories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_categories_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      product_reviews: {
        Row: {
          created_at: string | null
          id: number
          product_id: string | null
          rating: number
          review_text: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          rating: number
          review_text?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          rating?: number
          review_text?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_reviews_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      product_views: {
        Row: {
          created_at: string | null
          id: number
          ip_address: unknown | null
          product_id: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          ip_address?: unknown | null
          product_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          ip_address?: unknown | null
          product_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_views_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_views_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          created_at: string | null
          currency: string
          description_en: string | null
          description_tr: string | null
          id: string
          image: string | null
          is_available: boolean | null
          name_en: string | null
          name_tr: string
          price: number
          stock: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          currency?: string
          description_en?: string | null
          description_tr?: string | null
          id: string
          image?: string | null
          is_available?: boolean | null
          name_en?: string | null
          name_tr: string
          price: number
          stock?: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          currency?: string
          description_en?: string | null
          description_tr?: string | null
          id?: string
          image?: string | null
          is_available?: boolean | null
          name_en?: string | null
          name_tr?: string
          price?: number
          stock?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      sales_campaigns: {
        Row: {
          applicable_to: string
          coupon_code: string | null
          created_at: string | null
          currency: string | null
          description_en: string | null
          description_tr: string | null
          discount_amount: number
          discount_type: string
          id: string
          image: string | null
          is_active: boolean | null
          title_en: string | null
          title_tr: string
          usage_limit: number | null
          used_count: number | null
          validity_from: string
          validity_until: string
        }
        Insert: {
          applicable_to?: string
          coupon_code?: string | null
          created_at?: string | null
          currency?: string | null
          description_en?: string | null
          description_tr?: string | null
          discount_amount: number
          discount_type: string
          id: string
          image?: string | null
          is_active?: boolean | null
          title_en?: string | null
          title_tr: string
          usage_limit?: number | null
          used_count?: number | null
          validity_from: string
          validity_until: string
        }
        Update: {
          applicable_to?: string
          coupon_code?: string | null
          created_at?: string | null
          currency?: string | null
          description_en?: string | null
          description_tr?: string | null
          discount_amount?: number
          discount_type?: string
          id?: string
          image?: string | null
          is_active?: boolean | null
          title_en?: string | null
          title_tr?: string
          usage_limit?: number | null
          used_count?: number | null
          validity_from?: string
          validity_until?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          addresses: Json | null
          clerk_id: string | null
          created_at: string | null
          currency: string | null
          email: string | null
          id: string
          image: string | null
          is_admin_approved: boolean | null
          last_login: string | null
          loyalty_points: number | null
          name: string | null
          phone_number: string | null
          role: string | null
          status: string | null
          wallet_balance: number | null
        }
        Insert: {
          addresses?: Json | null
          clerk_id?: string | null
          created_at?: string | null
          currency?: string | null
          email?: string | null
          id: string
          image?: string | null
          is_admin_approved?: boolean | null
          last_login?: string | null
          loyalty_points?: number | null
          name?: string | null
          phone_number?: string | null
          role?: string | null
          status?: string | null
          wallet_balance?: number | null
        }
        Update: {
          addresses?: Json | null
          clerk_id?: string | null
          created_at?: string | null
          currency?: string | null
          email?: string | null
          id?: string
          image?: string | null
          is_admin_approved?: boolean | null
          last_login?: string | null
          loyalty_points?: number | null
          name?: string | null
          phone_number?: string | null
          role?: string | null
          status?: string | null
          wallet_balance?: number | null
        }
        Relationships: []
      }
      wallet_topup_requests: {
        Row: {
          amount: number
          approved_at: string | null
          created_at: string | null
          currency: string
          id: string
          payment_intent_id: string | null
          payment_method: string
          reason: string | null
          request_id: string
          status: string
          user_id: string | null
        }
        Insert: {
          amount: number
          approved_at?: string | null
          created_at?: string | null
          currency?: string
          id: string
          payment_intent_id?: string | null
          payment_method: string
          reason?: string | null
          request_id: string
          status?: string
          user_id?: string | null
        }
        Update: {
          amount?: number
          approved_at?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          payment_intent_id?: string | null
          payment_method?: string
          reason?: string | null
          request_id?: string
          status?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "wallet_topup_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      wishlists: {
        Row: {
          created_at: string | null
          id: number
          product_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          product_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "wishlists_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "wishlists_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
