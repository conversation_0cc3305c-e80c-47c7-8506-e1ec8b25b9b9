import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';
import { useServices } from '../../../hooks/useServices';
import { CreateProductData, UpdateProductData } from '../../../services/product.service';

interface Product {
  id: string;
  name_tr: string;
  name_en: string;
  description_tr: string;
  description_en: string;
  image: string;
  price: number;
  currency: string;
  stock: number;
  is_available: boolean;
  category_id: string;
  created_at: string;
  categories?: {
    name_tr: string;
  };
}

interface Category {
  id: string;
  name_tr: string;
}

export default function AdminProducts() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    name_tr: '',
    name_en: '',
    description_tr: '',
    description_en: '',
    image: '',
    price: '',
    currency: 'TRY',
    stock: '',
    category_id: '',
    is_available: true,
  });

  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();
  const { productService, categoryService } = useServices();

  // Ürünleri getir
  const { data: products, isLoading, error } = useQuery({
    queryKey: ['admin-products', searchQuery],
    queryFn: async () => {
      try {
        const result = await productService.getProducts({
          search: searchQuery,
        });
        console.log('Products loaded:', result?.length || 0);
        return result;
      } catch (error) {
        console.error('Error loading products:', error);
        throw error;
      }
    },
  });

  // Kategorileri getir
  const { data: categories } = useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      return await categoryService.getCategories({});
    },
  });

  // Ürün ekleme/güncelleme mutation
  const saveProductMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      // Basit validation
      if (!formData.name_tr.trim()) throw new Error('Ürün adı gereklidir');
      if (!formData.description_tr.trim()) throw new Error('Açıklama gereklidir');
      if (!formData.image.trim()) throw new Error('Görsel URL gereklidir');
      if (!formData.price || parseFloat(formData.price) <= 0) throw new Error('Geçerli bir fiyat giriniz');
      if (!formData.stock || parseInt(formData.stock) < 0) throw new Error('Geçerli bir stok miktarı giriniz');

      const productData: CreateProductData = {
        name_tr: formData.name_tr,
        name_en: formData.name_en,
        description_tr: formData.description_tr,
        description_en: formData.description_en,
        image: formData.image,
        price: parseFloat(formData.price),
        currency: formData.currency as 'TRY' | 'USD' | 'EUR',
        stock: parseInt(formData.stock),
        category_id: formData.category_id,
        is_available: formData.is_available,
      };

      if (editingProduct) {
        // Güncelleme
        const updateData: UpdateProductData = {
          id: editingProduct.id,
          ...productData,
        };
        return await productService.updateProduct(user.id, updateData);
      } else {
        // Yeni ekleme
        return await productService.createProduct(user.id, productData);
      }
    },
    onSuccess: () => {
      Alert.alert('Başarılı', editingProduct ? 'Ürün güncellendi!' : 'Ürün eklendi!');
      setShowAddModal(false);
      setEditingProduct(null);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['admin-products'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  // Ürün silme mutation
  const deleteProductMutation = useMutation({
    mutationFn: async (productId: string) => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      await productService.deleteProduct(user.id, productId);
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Ürün silindi!');
      queryClient.invalidateQueries({ queryKey: ['admin-products'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Ürün silinirken hata oluştu');
    },
  });

  const resetForm = () => {
    setFormData({
      name_tr: '',
      name_en: '',
      description_tr: '',
      description_en: '',
      image: '',
      price: '',
      currency: 'TRY',
      stock: '',
      category_id: '',
      is_available: true,
    });
  };

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setFormData({
      name_tr: product.name_tr,
      name_en: product.name_en || '',
      description_tr: product.description_tr,
      description_en: product.description_en || '',
      image: product.image,
      price: product.price.toString(),
      currency: product.currency,
      stock: product.stock.toString(),
      category_id: product.category_id || '',
      is_available: product.is_available,
    });
    setShowAddModal(true);
  };

  const handleDelete = (product: Product) => {
    Alert.alert(
      'Ürün Sil',
      `"${product.name_tr}" ürününü silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => deleteProductMutation.mutate(product.id)
        }
      ]
    );
  };

  const renderProduct = ({ item }: { item: Product }) => (
    <View style={styles.productCard}>
      <Image source={{ uri: item.image }} style={styles.productImage} />
      
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name_tr}</Text>
        <Text style={styles.productCategory}>{item.categories?.name_tr}</Text>
        <Text style={styles.productPrice}>₺{item.price.toFixed(2)}</Text>
        <Text style={styles.productStock}>Stok: {item.stock}</Text>
        
        <View style={styles.productStatus}>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: item.is_available ? '#34C759' : '#FF3B30' }
          ]}>
            <Text style={styles.statusText}>
              {item.is_available ? 'Aktif' : 'Pasif'}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.productActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="pencil" size={16} color="#007AFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF3B30' }]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Ürün Yönetimi',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <View style={styles.container}>
        {/* Arama ve Ekle */}
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Ürün ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              resetForm();
              setEditingProduct(null);
              setShowAddModal(true);
            }}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Ürün Listesi */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          <FlatList
            data={products}
            renderItem={renderProduct}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.productsList}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Ürün Ekleme/Düzenleme Modal */}
        <Modal
          visible={showAddModal}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Text style={styles.cancelText}>İptal</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>
                {editingProduct ? 'Ürün Düzenle' : 'Yeni Ürün'}
              </Text>
              
              <TouchableOpacity
                onPress={() => saveProductMutation.mutate()}
                disabled={saveProductMutation.isPending}
              >
                {saveProductMutation.isPending ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.saveText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Ürün Adı (TR) *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.name_tr}
                  onChangeText={(text) => setFormData({...formData, name_tr: text})}
                  placeholder="Türkçe ürün adı"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Ürün Adı (EN)</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.name_en}
                  onChangeText={(text) => setFormData({...formData, name_en: text})}
                  placeholder="İngilizce ürün adı"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Açıklama (TR) *</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.description_tr}
                  onChangeText={(text) => setFormData({...formData, description_tr: text})}
                  placeholder="Türkçe açıklama"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Görsel URL *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.image}
                  onChangeText={(text) => setFormData({...formData, image: text})}
                  placeholder="https://example.com/image.jpg"
                />
              </View>

              <View style={styles.formRow}>
                <View style={[styles.formGroup, { marginRight: 8 }]}>
                  <Text style={styles.formLabel}>Fiyat *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.price}
                    onChangeText={(text) => setFormData({...formData, price: text})}
                    placeholder="0.00"
                    keyboardType="numeric"
                  />
                </View>

                <View style={[styles.formGroup, { marginLeft: 8 }]}>
                  <Text style={styles.formLabel}>Stok *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.stock}
                    onChangeText={(text) => setFormData({...formData, stock: text})}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Kategori *</Text>
                <View style={styles.categoryContainer}>
                  {categories?.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={[
                        styles.categoryOption,
                        formData.category_id === category.id && styles.categoryOptionSelected
                      ]}
                      onPress={() => setFormData({...formData, category_id: category.id})}
                    >
                      <Text style={[
                        styles.categoryOptionText,
                        formData.category_id === category.id && styles.categoryOptionTextSelected
                      ]}>
                        {category.name_tr}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>



              <View style={styles.formGroup}>
                <TouchableOpacity
                  style={styles.switchContainer}
                  onPress={() => setFormData({...formData, is_available: !formData.is_available})}
                >
                  <Text style={styles.formLabel}>Ürün Aktif</Text>
                  <View style={[
                    styles.switch,
                    { backgroundColor: formData.is_available ? '#34C759' : '#ccc' }
                  ]}>
                    <View style={[
                      styles.switchThumb,
                      { transform: [{ translateX: formData.is_available ? 20 : 2 }] }
                    ]} />
                  </View>
                </TouchableOpacity>
              </View>


            </ScrollView>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 8,
    fontSize: 16,
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productsList: {
    padding: 16,
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  productInfo: {
    flex: 1,
    marginLeft: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  productCategory: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  productStock: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  productStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  productActions: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: '#f0f8ff',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
    flex: 1,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  categoryOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: 'white',
    marginRight: 8,
    marginBottom: 8,
  },
  categoryOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  categoryOptionText: {
    fontSize: 14,
    color: '#666',
  },
  categoryOptionTextSelected: {
    color: 'white',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switch: {
    width: 44,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
  },
  switchThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'white',
  },
});
