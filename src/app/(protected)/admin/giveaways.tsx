import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

interface Giveaway {
  id: string;
  title_tr: string;
  title_en: string;
  description_tr: string;
  description_en: string;
  image: string;
  rules_tr?: string;
  rules_en?: string;
  ticket_price: number;
  currency: string;
  total_tickets: number;
  tickets_sold: number;
  max_tickets_per_user: number;
  status: string;
  start_date: string;
  end_date: string;
  draw_date?: string;
  created_at: string;
}

interface Product {
  id: string;
  name_tr: string;
  image: string;
}

export default function AdminGiveaways() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingGiveaway, setEditingGiveaway] = useState<Giveaway | null>(null);
  const [formData, setFormData] = useState({
    title_tr: '',
    title_en: '',
    description_tr: '',
    description_en: '',
    image: '',
    rules_tr: '',
    rules_en: '',
    ticket_price: '',
    currency: 'TRY',
    total_tickets: '',
    max_tickets_per_user: '10',
    start_date: '',
    end_date: '',
    draw_date: '',
    status: 'draft',
  });

  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Çekilişleri getir
  const { data: giveaways, isLoading } = useQuery({
    queryKey: ['admin-giveaways', searchQuery],
    queryFn: async () => {
      let query = supabase
        .from('giveaways')
        .select('*')
        .order('created_at', { ascending: false });

      if (searchQuery) {
        query = query.or(`title_tr.ilike.%${searchQuery}%,title_en.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Giveaway[];
    },
  });

  // Ürünleri getir (ödül seçimi için)
  const { data: products } = useQuery({
    queryKey: ['products-for-giveaway'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('id, name_tr, image')
        .eq('is_available', true)
        .order('name_tr');

      if (error) throw error;
      return data as Product[];
    },
  });

  // Çekiliş ekleme/güncelleme mutation
  const saveGiveawayMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      // Basit validation
      if (!formData.title_tr.trim()) throw new Error('Çekiliş başlığı gereklidir');
      if (!formData.description_tr.trim()) throw new Error('Açıklama gereklidir');
      if (!formData.image.trim()) throw new Error('Görsel URL gereklidir');
      if (!formData.ticket_price || parseFloat(formData.ticket_price) <= 0) throw new Error('Geçerli bir bilet fiyatı giriniz');
      if (!formData.total_tickets || parseInt(formData.total_tickets) <= 0) throw new Error('Geçerli bir toplam bilet sayısı giriniz');
      if (!formData.start_date) throw new Error('Başlangıç tarihi gereklidir');
      if (!formData.end_date) throw new Error('Bitiş tarihi gereklidir');

      const giveawayData = {
        title_tr: formData.title_tr,
        title_en: formData.title_en || formData.title_tr,
        description_tr: formData.description_tr,
        description_en: formData.description_en || formData.description_tr,
        image: formData.image,
        rules_tr: formData.rules_tr,
        rules_en: formData.rules_en || formData.rules_tr,
        ticket_price: parseFloat(formData.ticket_price),
        currency: formData.currency,
        total_tickets: parseInt(formData.total_tickets),
        max_tickets_per_user: parseInt(formData.max_tickets_per_user),
        start_date: formData.start_date,
        end_date: formData.end_date,
        draw_date: formData.draw_date || null,
        status: formData.status,
        tickets_sold: 0,
      };

      if (editingGiveaway) {
        // Güncelleme
        const { data, error } = await supabase
          .from('giveaways')
          .update(giveawayData)
          .eq('id', editingGiveaway.id)
          .select();

        if (error) throw error;

        // Admin log
        await supabase.from('admin_activity_log').insert({
          admin_id: user.id,
          action: 'update_giveaway',
          target_type: 'giveaway',
          target_id: editingGiveaway.id,
          details: { old_data: editingGiveaway, new_data: giveawayData },
        });

        return data;
      } else {
        // Yeni ekleme
        const { data, error } = await supabase
          .from('giveaways')
          .insert(giveawayData)
          .select();

        if (error) throw error;

        // Admin log
        await supabase.from('admin_activity_log').insert({
          admin_id: user.id,
          action: 'create_giveaway',
          target_type: 'giveaway',
          target_id: data[0].id,
          details: { giveaway_data: giveawayData },
        });

        return data;
      }
    },
    onSuccess: () => {
      Alert.alert('Başarılı', editingGiveaway ? 'Çekiliş güncellendi!' : 'Çekiliş eklendi!');
      setShowAddModal(false);
      setEditingGiveaway(null);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['admin-giveaways'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  // Çekiliş silme mutation
  const deleteGiveawayMutation = useMutation({
    mutationFn: async (giveawayId: string) => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      const { error } = await supabase
        .from('giveaways')
        .delete()
        .eq('id', giveawayId);

      if (error) throw error;

      // Admin log
      await supabase.from('admin_activity_log').insert({
        admin_id: user.id,
        action: 'delete_giveaway',
        target_type: 'giveaway',
        target_id: giveawayId,
        details: { deleted_at: new Date().toISOString() },
      });
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Çekiliş silindi!');
      queryClient.invalidateQueries({ queryKey: ['admin-giveaways'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Çekiliş silinirken hata oluştu');
    },
  });

  const resetForm = () => {
    setFormData({
      title_tr: '',
      title_en: '',
      description_tr: '',
      description_en: '',
      image: '',
      rules_tr: '',
      rules_en: '',
      ticket_price: '',
      currency: 'TRY',
      total_tickets: '',
      max_tickets_per_user: '10',
      start_date: '',
      end_date: '',
      draw_date: '',
      status: 'draft',
    });
  };

  const handleEdit = (giveaway: Giveaway) => {
    setEditingGiveaway(giveaway);
    setFormData({
      title_tr: giveaway.title_tr,
      title_en: giveaway.title_en || '',
      description_tr: giveaway.description_tr,
      description_en: giveaway.description_en || '',
      image: giveaway.image,
      rules_tr: giveaway.rules_tr || '',
      rules_en: giveaway.rules_en || '',
      ticket_price: giveaway.ticket_price.toString(),
      currency: giveaway.currency,
      total_tickets: giveaway.total_tickets.toString(),
      max_tickets_per_user: giveaway.max_tickets_per_user.toString(),
      start_date: giveaway.start_date.split('T')[0],
      end_date: giveaway.end_date.split('T')[0],
      draw_date: giveaway.draw_date ? giveaway.draw_date.split('T')[0] : '',
      status: giveaway.status,
    });
    setShowAddModal(true);
  };

  const handleDelete = (giveaway: Giveaway) => {
    Alert.alert(
      'Çekiliş Sil',
      `"${giveaway.title_tr}" çekilişini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => deleteGiveawayMutation.mutate(giveaway.id)
        }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#34C759';
      case 'draft': return '#FF9500';
      case 'completed': return '#007AFF';
      case 'cancelled': return '#FF3B30';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'draft': return 'Taslak';
      case 'completed': return 'Tamamlandı';
      case 'cancelled': return 'İptal Edildi';
      default: return status;
    }
  };

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const renderGiveaway = ({ item }: { item: Giveaway }) => (
    <View style={styles.giveawayCard}>
      <Image source={{ uri: item.image }} style={styles.giveawayImage} />
      
      <View style={styles.giveawayInfo}>
        <Text style={styles.giveawayTitle}>{item.title_tr}</Text>
        <Text style={styles.giveawayPrice}>{formatPrice(item.ticket_price, item.currency)}</Text>
        <Text style={styles.giveawayTickets}>
          {item.tickets_sold}/{item.total_tickets} bilet
        </Text>
        
        <View style={styles.giveawayStatus}>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: getStatusColor(item.status) }
          ]}>
            <Text style={styles.statusText}>
              {getStatusText(item.status)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.giveawayActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="pencil" size={16} color="#007AFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF3B30' }]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Çekiliş Yönetimi',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <View style={styles.container}>
        {/* Arama ve Ekle */}
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Çekiliş ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              resetForm();
              setEditingGiveaway(null);
              setShowAddModal(true);
            }}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Çekiliş Listesi */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          <FlatList
            data={giveaways}
            renderItem={renderGiveaway}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.giveawaysList}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Çekiliş Ekleme/Düzenleme Modal */}
        <Modal
          visible={showAddModal}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Text style={styles.cancelText}>İptal</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>
                {editingGiveaway ? 'Çekiliş Düzenle' : 'Yeni Çekiliş'}
              </Text>
              
              <TouchableOpacity
                onPress={() => saveGiveawayMutation.mutate()}
                disabled={saveGiveawayMutation.isPending}
              >
                {saveGiveawayMutation.isPending ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.saveText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Çekiliş Başlığı (TR) *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.title_tr}
                  onChangeText={(text) => setFormData({...formData, title_tr: text})}
                  placeholder="Türkçe çekiliş başlığı"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Açıklama (TR) *</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.description_tr}
                  onChangeText={(text) => setFormData({...formData, description_tr: text})}
                  placeholder="Türkçe açıklama"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Görsel URL *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.image}
                  onChangeText={(text) => setFormData({...formData, image: text})}
                  placeholder="https://example.com/image.jpg"
                />
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Bilet Fiyatı *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.ticket_price}
                    onChangeText={(text) => setFormData({...formData, ticket_price: text})}
                    placeholder="10.00"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Toplam Bilet *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.total_tickets}
                    onChangeText={(text) => setFormData({...formData, total_tickets: text})}
                    placeholder="1000"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Başlangıç Tarihi *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.start_date}
                    onChangeText={(text) => setFormData({...formData, start_date: text})}
                    placeholder="2024-01-01"
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Bitiş Tarihi *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.end_date}
                    onChangeText={(text) => setFormData({...formData, end_date: text})}
                    placeholder="2024-01-31"
                  />
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Durum</Text>
                <View style={styles.statusContainer}>
                  {[
                    { key: 'draft', label: 'Taslak' },
                    { key: 'active', label: 'Aktif' },
                    { key: 'completed', label: 'Tamamlandı' },
                    { key: 'cancelled', label: 'İptal Edildi' },
                  ].map((status) => (
                    <TouchableOpacity
                      key={status.key}
                      style={[
                        styles.statusOption,
                        formData.status === status.key && styles.statusOptionSelected
                      ]}
                      onPress={() => setFormData({...formData, status: status.key})}
                    >
                      <Text style={[
                        styles.statusOptionText,
                        formData.status === status.key && styles.statusOptionTextSelected
                      ]}>
                        {status.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 8,
    fontSize: 16,
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  giveawaysList: {
    padding: 16,
  },
  giveawayCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  giveawayImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  giveawayInfo: {
    flex: 1,
    marginLeft: 12,
  },
  giveawayTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  giveawayPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  giveawayTickets: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  giveawayStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  giveawayActions: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: '#f0f8ff',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: 'white',
    marginRight: 8,
    marginBottom: 8,
  },
  statusOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  statusOptionText: {
    fontSize: 14,
    color: '#666',
  },
  statusOptionTextSelected: {
    color: 'white',
  },
});
