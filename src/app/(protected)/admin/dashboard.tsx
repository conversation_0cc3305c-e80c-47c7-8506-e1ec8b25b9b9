import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

interface DashboardStats {
  totalProducts: number;
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  activeGiveaways: number;
  activeAuctions: number;
  pendingWalletRequests: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  created_at: string;
  last_login?: string;
}

export default function AdminDashboard() {
  const [refreshing, setRefreshing] = useState(false);
  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Kullanıcının admin olup olmadığını kontrol et
  const { data: userRole, isLoading: roleLoading } = useQuery({
    queryKey: ['user-role', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('users')
        .select('role, role_permissions')
        .eq('clerk_id', user.id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!user?.id,
  });

  // Dashboard istatistiklerini getir
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['admin-dashboard-stats'],
    queryFn: async () => {
      const [
        productsResult,
        usersResult,
        ordersResult,
        giveawaysResult,
        auctionsResult,
        walletRequestsResult
      ] = await Promise.all([
        supabase.from('products').select('id', { count: 'exact' }),
        supabase.from('users').select('id', { count: 'exact' }),
        supabase.from('orders').select('id, total_amount', { count: 'exact' }),
        supabase.from('giveaways').select('id', { count: 'exact' }).eq('status', 'active'),
        supabase.from('auctions').select('id', { count: 'exact' }).eq('status', 'active'),
        supabase.from('wallet_topup_requests').select('id', { count: 'exact' }).eq('status', 'pending')
      ]);

      const totalRevenue = ordersResult.data?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0;

      return {
        totalProducts: productsResult.count || 0,
        totalUsers: usersResult.count || 0,
        totalOrders: ordersResult.count || 0,
        totalRevenue,
        activeGiveaways: giveawaysResult.count || 0,
        activeAuctions: auctionsResult.count || 0,
        pendingWalletRequests: walletRequestsResult.count || 0,
      } as DashboardStats;
    },
    enabled: userRole?.role === 'admin',
  });

  // Son kullanıcıları getir
  const { data: recentUsers } = useQuery({
    queryKey: ['recent-users'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, role, created_at, last_login')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data as User[];
    },
    enabled: userRole?.role === 'admin',
  });

  const onRefresh = async () => {
    setRefreshing(true);
    await queryClient.invalidateQueries({ queryKey: ['admin-dashboard-stats'] });
    await queryClient.invalidateQueries({ queryKey: ['recent-users'] });
    setRefreshing(false);
  };

  const formatPrice = (price: number) => {
    return `₺${price.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return '#FF3B30';
      case 'finance_manager': return '#FF9500';
      case 'moderator': return '#007AFF';
      default: return '#34C759';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin': return 'Admin';
      case 'finance_manager': return 'Finans Yöneticisi';
      case 'moderator': return 'Moderatör';
      default: return 'Kullanıcı';
    }
  };

  if (roleLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Yetki kontrol ediliyor...</Text>
      </View>
    );
  }

  if (userRole?.role !== 'admin') {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="shield-outline" size={64} color="#FF3B30" />
        <Text style={styles.errorTitle}>Erişim Reddedildi</Text>
        <Text style={styles.errorText}>Bu sayfaya erişim yetkiniz bulunmamaktadır.</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Admin Dashboard',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <ScrollView 
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* İstatistikler */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Genel İstatistikler</Text>
          
          {statsLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Ionicons name="cube-outline" size={24} color="#007AFF" />
                <Text style={styles.statNumber}>{stats?.totalProducts || 0}</Text>
                <Text style={styles.statLabel}>Ürün</Text>
              </View>
              
              <View style={styles.statCard}>
                <Ionicons name="people-outline" size={24} color="#34C759" />
                <Text style={styles.statNumber}>{stats?.totalUsers || 0}</Text>
                <Text style={styles.statLabel}>Kullanıcı</Text>
              </View>
              
              <View style={styles.statCard}>
                <Ionicons name="receipt-outline" size={24} color="#FF9500" />
                <Text style={styles.statNumber}>{stats?.totalOrders || 0}</Text>
                <Text style={styles.statLabel}>Sipariş</Text>
              </View>
              
              <View style={styles.statCard}>
                <Ionicons name="cash-outline" size={24} color="#FF3B30" />
                <Text style={styles.statNumber}>{formatPrice(stats?.totalRevenue || 0)}</Text>
                <Text style={styles.statLabel}>Gelir</Text>
              </View>
              
              <View style={styles.statCard}>
                <Ionicons name="gift-outline" size={24} color="#AF52DE" />
                <Text style={styles.statNumber}>{stats?.activeGiveaways || 0}</Text>
                <Text style={styles.statLabel}>Aktif Çekiliş</Text>
              </View>
              
              <View style={styles.statCard}>
                <Ionicons name="hammer-outline" size={24} color="#FF2D92" />
                <Text style={styles.statNumber}>{stats?.activeAuctions || 0}</Text>
                <Text style={styles.statLabel}>Aktif Açık Artırma</Text>
              </View>
            </View>
          )}
        </View>

        {/* Hızlı Eylemler */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Hızlı Eylemler</Text>
          
          <View style={styles.actionsGrid}>
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={() => router.push('/admin/products')}
            >
              <Ionicons name="cube-outline" size={32} color="#007AFF" />
              <Text style={styles.actionTitle}>Ürün Yönetimi</Text>
              <Text style={styles.actionSubtitle}>Ürün ekle, düzenle, sil</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={() => router.push('/admin/users')}
            >
              <Ionicons name="people-outline" size={32} color="#34C759" />
              <Text style={styles.actionTitle}>Kullanıcı Yönetimi</Text>
              <Text style={styles.actionSubtitle}>Kullanıcı rolleri ve izinler</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionCard}
              onPress={() => router.push('/admin/wallet-requests')}
            >
              <Ionicons name="wallet-outline" size={32} color="#FF9500" />
              <Text style={styles.actionTitle}>Cüzdan Talepleri</Text>
              <Text style={styles.actionSubtitle}>{stats?.pendingWalletRequests || 0} bekleyen</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/admin/categories')}
            >
              <Ionicons name="grid-outline" size={32} color="#AF52DE" />
              <Text style={styles.actionTitle}>Kategori Yönetimi</Text>
              <Text style={styles.actionSubtitle}>Kategori ekle, düzenle, sil</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/admin/giveaways')}
            >
              <Ionicons name="gift-outline" size={32} color="#FF2D92" />
              <Text style={styles.actionTitle}>Çekiliş Yönetimi</Text>
              <Text style={styles.actionSubtitle}>Çekiliş ekle ve yönet</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/admin/auctions')}
            >
              <Ionicons name="hammer-outline" size={32} color="#FF9500" />
              <Text style={styles.actionTitle}>Açık Artırma</Text>
              <Text style={styles.actionSubtitle}>Açık artırma ekle ve yönet</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/debug')}
            >
              <Ionicons name="bug-outline" size={32} color="#FF3B30" />
              <Text style={styles.actionTitle}>Debug</Text>
              <Text style={styles.actionSubtitle}>Sistem durumu ve testler</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/admin/analytics')}
            >
              <Ionicons name="analytics-outline" size={32} color="#5856D6" />
              <Text style={styles.actionTitle}>Analitik</Text>
              <Text style={styles.actionSubtitle}>Raporlar ve istatistikler</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Son Kullanıcılar */}
        <View style={styles.usersContainer}>
          <Text style={styles.sectionTitle}>Son Kayıt Olan Kullanıcılar</Text>
          
          {recentUsers?.map((user) => (
            <View key={user.id} style={styles.userCard}>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>{user.name}</Text>
                <Text style={styles.userEmail}>{user.email}</Text>
                <Text style={styles.userDate}>Kayıt: {formatDate(user.created_at)}</Text>
              </View>
              <View style={[styles.roleBadge, { backgroundColor: getRoleColor(user.role) }]}>
                <Text style={styles.roleText}>{getRoleText(user.role)}</Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  actionsContainer: {
    backgroundColor: 'white',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  usersContainer: {
    backgroundColor: 'white',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  userCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  userDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
});
