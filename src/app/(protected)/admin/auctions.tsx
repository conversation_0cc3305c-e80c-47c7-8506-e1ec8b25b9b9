import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

interface Auction {
  id: string;
  product_id?: string;
  description_tr: string;
  description_en?: string;
  image: string;
  starting_bid: number;
  current_bid: number;
  bid_increment_amount: number;
  currency: string;
  start_time: string;
  end_time: string;
  status: string;
  created_at: string;
  products?: {
    name_tr: string;
    image: string;
  };
}

interface Product {
  id: string;
  name_tr: string;
  image: string;
  price: number;
}

export default function AdminAuctions() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);
  const [formData, setFormData] = useState({
    product_id: '',
    description_tr: '',
    description_en: '',
    image: '',
    starting_bid: '',
    bid_increment_amount: '10',
    currency: 'TRY',
    start_time: '',
    end_time: '',
    status: 'draft',
  });

  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Açık artırmaları getir
  const { data: auctions, isLoading } = useQuery({
    queryKey: ['admin-auctions', searchQuery],
    queryFn: async () => {
      let query = supabase
        .from('auctions')
        .select(`
          *,
          products(name_tr, image)
        `)
        .order('created_at', { ascending: false });

      if (searchQuery) {
        query = query.or(`description_tr.ilike.%${searchQuery}%,description_en.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Auction[];
    },
  });

  // Ürünleri getir
  const { data: products } = useQuery({
    queryKey: ['products-for-auction'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('id, name_tr, image, price')
        .eq('is_available', true)
        .order('name_tr');

      if (error) throw error;
      return data as Product[];
    },
  });

  // Açık artırma ekleme/güncelleme mutation
  const saveAuctionMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      // Basit validation
      if (!formData.description_tr.trim()) throw new Error('Açıklama gereklidir');
      if (!formData.image.trim()) throw new Error('Görsel URL gereklidir');
      if (!formData.starting_bid || parseFloat(formData.starting_bid) <= 0) throw new Error('Geçerli bir başlangıç teklifi giriniz');
      if (!formData.bid_increment_amount || parseFloat(formData.bid_increment_amount) <= 0) throw new Error('Geçerli bir artış miktarı giriniz');
      if (!formData.start_time) throw new Error('Başlangıç zamanı gereklidir');
      if (!formData.end_time) throw new Error('Bitiş zamanı gereklidir');

      const auctionData = {
        product_id: formData.product_id || null,
        description_tr: formData.description_tr,
        description_en: formData.description_en || formData.description_tr,
        image: formData.image,
        starting_bid: parseFloat(formData.starting_bid),
        current_bid: parseFloat(formData.starting_bid),
        bid_increment_amount: parseFloat(formData.bid_increment_amount),
        currency: formData.currency,
        start_time: formData.start_time,
        end_time: formData.end_time,
        status: formData.status,
      };

      if (editingAuction) {
        // Güncelleme
        const { data, error } = await supabase
          .from('auctions')
          .update(auctionData)
          .eq('id', editingAuction.id)
          .select();

        if (error) throw error;

        // Admin log
        await supabase.from('admin_activity_log').insert({
          admin_id: user.id,
          action: 'update_auction',
          target_type: 'auction',
          target_id: editingAuction.id,
          details: { old_data: editingAuction, new_data: auctionData },
        });

        return data;
      } else {
        // Yeni ekleme
        const { data, error } = await supabase
          .from('auctions')
          .insert(auctionData)
          .select();

        if (error) throw error;

        // Admin log
        await supabase.from('admin_activity_log').insert({
          admin_id: user.id,
          action: 'create_auction',
          target_type: 'auction',
          target_id: data[0].id,
          details: { auction_data: auctionData },
        });

        return data;
      }
    },
    onSuccess: () => {
      Alert.alert('Başarılı', editingAuction ? 'Açık artırma güncellendi!' : 'Açık artırma eklendi!');
      setShowAddModal(false);
      setEditingAuction(null);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['admin-auctions'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  // Açık artırma silme mutation
  const deleteAuctionMutation = useMutation({
    mutationFn: async (auctionId: string) => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      const { error } = await supabase
        .from('auctions')
        .delete()
        .eq('id', auctionId);

      if (error) throw error;

      // Admin log
      await supabase.from('admin_activity_log').insert({
        admin_id: user.id,
        action: 'delete_auction',
        target_type: 'auction',
        target_id: auctionId,
        details: { deleted_at: new Date().toISOString() },
      });
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Açık artırma silindi!');
      queryClient.invalidateQueries({ queryKey: ['admin-auctions'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Açık artırma silinirken hata oluştu');
    },
  });

  const resetForm = () => {
    setFormData({
      product_id: '',
      description_tr: '',
      description_en: '',
      image: '',
      starting_bid: '',
      bid_increment_amount: '10',
      currency: 'TRY',
      start_time: '',
      end_time: '',
      status: 'draft',
    });
  };

  const handleEdit = (auction: Auction) => {
    setEditingAuction(auction);
    setFormData({
      product_id: auction.product_id || '',
      description_tr: auction.description_tr,
      description_en: auction.description_en || '',
      image: auction.image,
      starting_bid: auction.starting_bid.toString(),
      bid_increment_amount: auction.bid_increment_amount.toString(),
      currency: auction.currency,
      start_time: auction.start_time.split('T')[0] + 'T' + auction.start_time.split('T')[1].slice(0, 5),
      end_time: auction.end_time.split('T')[0] + 'T' + auction.end_time.split('T')[1].slice(0, 5),
      status: auction.status,
    });
    setShowAddModal(true);
  };

  const handleDelete = (auction: Auction) => {
    const title = auction.products?.name_tr || `Açık Artırma ${auction.id.slice(-6)}`;
    Alert.alert(
      'Açık Artırma Sil',
      `"${title}" açık artırmasını silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => deleteAuctionMutation.mutate(auction.id)
        }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#34C759';
      case 'draft': return '#FF9500';
      case 'completed': return '#007AFF';
      case 'cancelled': return '#FF3B30';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'draft': return 'Taslak';
      case 'completed': return 'Tamamlandı';
      case 'cancelled': return 'İptal Edildi';
      default: return status;
    }
  };

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const renderAuction = ({ item }: { item: Auction }) => (
    <View style={styles.auctionCard}>
      <Image 
        source={{ uri: item.products?.image || item.image }} 
        style={styles.auctionImage} 
      />
      
      <View style={styles.auctionInfo}>
        <Text style={styles.auctionTitle}>
          {item.products?.name_tr || `Açık Artırma ${item.id.slice(-6)}`}
        </Text>
        <Text style={styles.auctionPrice}>
          Başlangıç: {formatPrice(item.starting_bid, item.currency)}
        </Text>
        <Text style={styles.auctionCurrentBid}>
          Mevcut: {formatPrice(item.current_bid, item.currency)}
        </Text>
        
        <View style={styles.auctionStatus}>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: getStatusColor(item.status) }
          ]}>
            <Text style={styles.statusText}>
              {getStatusText(item.status)}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.auctionActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="pencil" size={16} color="#007AFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF3B30' }]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Açık Artırma Yönetimi',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <View style={styles.container}>
        {/* Arama ve Ekle */}
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Açık artırma ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              resetForm();
              setEditingAuction(null);
              setShowAddModal(true);
            }}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Açık Artırma Listesi */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          <FlatList
            data={auctions}
            renderItem={renderAuction}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.auctionsList}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Açık Artırma Ekleme/Düzenleme Modal */}
        <Modal
          visible={showAddModal}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Text style={styles.cancelText}>İptal</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>
                {editingAuction ? 'Açık Artırma Düzenle' : 'Yeni Açık Artırma'}
              </Text>
              
              <TouchableOpacity
                onPress={() => saveAuctionMutation.mutate()}
                disabled={saveAuctionMutation.isPending}
              >
                {saveAuctionMutation.isPending ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.saveText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Ürün Seç</Text>
                <View style={styles.productContainer}>
                  {products?.map((product) => (
                    <TouchableOpacity
                      key={product.id}
                      style={[
                        styles.productOption,
                        formData.product_id === product.id && styles.productOptionSelected
                      ]}
                      onPress={() => {
                        setFormData({
                          ...formData, 
                          product_id: product.id,
                          image: product.image,
                          starting_bid: product.price.toString(),
                        });
                      }}
                    >
                      <Image source={{ uri: product.image }} style={styles.productOptionImage} />
                      <Text style={[
                        styles.productOptionText,
                        formData.product_id === product.id && styles.productOptionTextSelected
                      ]}>
                        {product.name_tr}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Açıklama (TR) *</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.description_tr}
                  onChangeText={(text) => setFormData({...formData, description_tr: text})}
                  placeholder="Türkçe açıklama"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Görsel URL</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.image}
                  onChangeText={(text) => setFormData({...formData, image: text})}
                  placeholder="https://example.com/image.jpg"
                />
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Başlangıç Teklifi *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.starting_bid}
                    onChangeText={(text) => setFormData({...formData, starting_bid: text})}
                    placeholder="100.00"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Artış Miktarı *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.bid_increment_amount}
                    onChangeText={(text) => setFormData({...formData, bid_increment_amount: text})}
                    placeholder="10.00"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Başlangıç Zamanı *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.start_time}
                    onChangeText={(text) => setFormData({...formData, start_time: text})}
                    placeholder="2024-01-01T10:00"
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Bitiş Zamanı *</Text>
                  <TextInput
                    style={styles.formInput}
                    value={formData.end_time}
                    onChangeText={(text) => setFormData({...formData, end_time: text})}
                    placeholder="2024-01-01T18:00"
                  />
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Durum</Text>
                <View style={styles.statusContainer}>
                  {[
                    { key: 'draft', label: 'Taslak' },
                    { key: 'active', label: 'Aktif' },
                    { key: 'completed', label: 'Tamamlandı' },
                    { key: 'cancelled', label: 'İptal Edildi' },
                  ].map((status) => (
                    <TouchableOpacity
                      key={status.key}
                      style={[
                        styles.statusOption,
                        formData.status === status.key && styles.statusOptionSelected
                      ]}
                      onPress={() => setFormData({...formData, status: status.key})}
                    >
                      <Text style={[
                        styles.statusOptionText,
                        formData.status === status.key && styles.statusOptionTextSelected
                      ]}>
                        {status.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 8,
    fontSize: 16,
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  auctionsList: {
    padding: 16,
  },
  auctionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  auctionImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  auctionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  auctionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  auctionPrice: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  auctionCurrentBid: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  auctionStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  auctionActions: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: '#f0f8ff',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  productContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productOption: {
    width: '48%',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: 'white',
    alignItems: 'center',
  },
  productOptionSelected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#007AFF',
  },
  productOptionImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 8,
  },
  productOptionText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  productOptionTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: 'white',
    marginRight: 8,
    marginBottom: 8,
  },
  statusOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  statusOptionText: {
    fontSize: 14,
    color: '#666',
  },
  statusOptionTextSelected: {
    color: 'white',
  },
});
