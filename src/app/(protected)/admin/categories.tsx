import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  Modal,
  ScrollView,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';
import { useServices } from '../../../hooks/useServices';
import { Category as ServiceCategory, CreateCategoryData, UpdateCategoryData } from '../../../services';

export default function AdminCategories() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ServiceCategory | null>(null);
  const [formData, setFormData] = useState({
    name_tr: '',
    name_en: '',
    description_tr: '',
    description_en: '',
    image: '',
    is_active: true,
  });

  const { categoryService } = useServices();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Kategorileri getir
  const { data: categories, isLoading } = useQuery({
    queryKey: ['admin-categories', searchQuery],
    queryFn: async () => {
      return await categoryService.getCategories({
        search: searchQuery,
      });
    },
  });

  // Kategori ekleme/güncelleme mutation
  const saveCategoryMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      const categoryData: CreateCategoryData = {
        name_tr: formData.name_tr,
        name_en: formData.name_en,
        description_tr: formData.description_tr,
        description_en: formData.description_en,
        image: formData.image,
        is_active: formData.is_active,
      };

      if (editingCategory) {
        // Güncelleme
        const updateData: UpdateCategoryData = {
          id: editingCategory.id,
          ...categoryData,
        };
        return await categoryService.updateCategory(user.id, updateData);
      } else {
        // Yeni ekleme
        return await categoryService.createCategory(user.id, categoryData);
      }
    },
    onSuccess: () => {
      Alert.alert('Başarılı', editingCategory ? 'Kategori güncellendi!' : 'Kategori eklendi!');
      setShowAddModal(false);
      setEditingCategory(null);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['admin-categories'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  // Kategori silme mutation
  const deleteCategoryMutation = useMutation({
    mutationFn: async (categoryId: string) => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      await categoryService.deleteCategory(user.id, categoryId);
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Kategori silindi!');
      queryClient.invalidateQueries({ queryKey: ['admin-categories'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Kategori silinirken hata oluştu');
    },
  });

  const resetForm = () => {
    setFormData({
      name_tr: '',
      name_en: '',
      description_tr: '',
      description_en: '',
      image: '',
      is_active: true,
    });
  };

  const handleEdit = (category: ServiceCategory) => {
    setEditingCategory(category);
    setFormData({
      name_tr: category.name_tr,
      name_en: category.name_en || '',
      description_tr: category.description_tr || '',
      description_en: category.description_en || '',
      image: category.image,
      is_active: category.is_active,
    });
    setShowAddModal(true);
  };

  const handleDelete = (category: ServiceCategory) => {
    Alert.alert(
      'Kategori Sil',
      `"${category.name_tr}" kategorisini silmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Sil', 
          style: 'destructive',
          onPress: () => deleteCategoryMutation.mutate(category.id)
        }
      ]
    );
  };

  const renderCategory = ({ item }: { item: ServiceCategory }) => (
    <View style={styles.categoryCard}>
      <Image source={{ uri: item.image }} style={styles.categoryImage} />
      
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name_tr}</Text>
        {item.description_tr && (
          <Text style={styles.categoryDescription} numberOfLines={2}>
            {item.description_tr}
          </Text>
        )}
        
        <View style={styles.categoryStatus}>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: item.is_active ? '#34C759' : '#FF3B30' }
          ]}>
            <Text style={styles.statusText}>
              {item.is_active ? 'Aktif' : 'Pasif'}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.categoryActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="pencil" size={16} color="#007AFF" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#FF3B30' }]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Kategori Yönetimi',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <View style={styles.container}>
        {/* Arama ve Ekle */}
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Kategori ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              resetForm();
              setEditingCategory(null);
              setShowAddModal(true);
            }}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>

        {/* Kategori Listesi */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.categoriesList}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Kategori Ekleme/Düzenleme Modal */}
        <Modal
          visible={showAddModal}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={() => setShowAddModal(false)}>
                <Text style={styles.cancelText}>İptal</Text>
              </TouchableOpacity>
              
              <Text style={styles.modalTitle}>
                {editingCategory ? 'Kategori Düzenle' : 'Yeni Kategori'}
              </Text>
              
              <TouchableOpacity
                onPress={() => saveCategoryMutation.mutate()}
                disabled={saveCategoryMutation.isPending}
              >
                {saveCategoryMutation.isPending ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.saveText}>Kaydet</Text>
                )}
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Kategori Adı (TR) *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.name_tr}
                  onChangeText={(text) => setFormData({...formData, name_tr: text})}
                  placeholder="Türkçe kategori adı"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Kategori Adı (EN)</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.name_en}
                  onChangeText={(text) => setFormData({...formData, name_en: text})}
                  placeholder="İngilizce kategori adı"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Açıklama (TR)</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.description_tr}
                  onChangeText={(text) => setFormData({...formData, description_tr: text})}
                  placeholder="Türkçe açıklama"
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Görsel URL *</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.image}
                  onChangeText={(text) => setFormData({...formData, image: text})}
                  placeholder="https://example.com/image.jpg"
                />
              </View>

              <View style={styles.formGroup}>
                <TouchableOpacity
                  style={styles.switchContainer}
                  onPress={() => setFormData({...formData, is_active: !formData.is_active})}
                >
                  <Text style={styles.formLabel}>Kategori Aktif</Text>
                  <View style={[
                    styles.switch,
                    { backgroundColor: formData.is_active ? '#34C759' : '#ccc' }
                  ]}>
                    <View style={[
                      styles.switchThumb,
                      { transform: [{ translateX: formData.is_active ? 20 : 2 }] }
                    ]} />
                  </View>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </Modal>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 8,
    fontSize: 16,
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesList: {
    padding: 16,
  },
  categoryCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  categoryInfo: {
    flex: 1,
    marginLeft: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  categoryStatus: {
    flexDirection: 'row',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  categoryActions: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    backgroundColor: '#f0f8ff',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  cancelText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switch: {
    width: 44,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
  },
  switchThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'white',
  },
});
