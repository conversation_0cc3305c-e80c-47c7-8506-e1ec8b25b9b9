import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '@clerk/clerk-expo';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';

interface WalletRequest {
  id: string;
  user_id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'approved' | 'rejected';
  payment_method: string;
  created_at: string;
  users: {
    name: string;
    email: string;
  };
}

export default function WalletRequestsScreen() {
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const { user } = useUser();
  const supabase = useSupabase();
  const queryClient = useQueryClient();

  // Para yükleme taleplerini getir
  const { data: requests, isLoading } = useQuery({
    queryKey: ['wallet-requests', filter],
    queryFn: async () => {
      let query = supabase
        .from('wallet_topup_requests')
        .select(`
          *,
          users(name, email)
        `)
        .order('created_at', { ascending: false });

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as WalletRequest[];
    },
  });

  // Talep onaylama/reddetme
  const updateRequestMutation = useMutation({
    mutationFn: async ({ requestId, status, amount, userId, currency }: {
      requestId: string;
      status: 'approved' | 'rejected';
      amount: number;
      userId: string;
      currency: string;
    }) => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');

      // Talebi güncelle
      const { error: requestError } = await supabase
        .from('wallet_topup_requests')
        .update({
          status,
          processed_by: user.id,
        })
        .eq('id', requestId);

      if (requestError) throw requestError;

      // Eğer onaylandıysa kullanıcının bakiyesini artır
      if (status === 'approved') {
        const { error: balanceError } = await supabase.rpc('update_user_balance', {
          user_clerk_id: userId,
          amount_to_add: amount
        });

        if (balanceError) throw balanceError;
      }

      // Admin log
      await supabase.from('admin_activity_log').insert({
        admin_id: user.id,
        action: status === 'approved' ? 'approve_wallet_topup' : 'reject_wallet_topup',
        target_type: 'wallet_topup_request',
        target_id: requestId,
        details: { amount, currency, user_id: userId },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['wallet-requests'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  const handleApprove = (request: WalletRequest) => {
    Alert.alert(
      'Talebi Onayla',
      `${request.users.name} kullanıcısının ${request.amount} ${request.currency} para yükleme talebini onaylamak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Onayla',
          style: 'default',
          onPress: () => updateRequestMutation.mutate({
            requestId: request.id,
            status: 'approved',
            amount: request.amount,
            userId: request.user_id,
            currency: request.currency,
          }),
        },
      ]
    );
  };

  const handleReject = (request: WalletRequest) => {
    Alert.alert(
      'Talebi Reddet',
      `${request.users.name} kullanıcısının para yükleme talebini reddetmek istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Reddet',
          style: 'destructive',
          onPress: () => updateRequestMutation.mutate({
            requestId: request.id,
            status: 'rejected',
            amount: request.amount,
            userId: request.user_id,
            currency: request.currency,
          }),
        },
      ]
    );
  };

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return '#34C759';
      case 'rejected': return '#FF3B30';
      case 'pending': return '#FF9500';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'Onaylandı';
      case 'rejected': return 'Reddedildi';
      case 'pending': return 'Beklemede';
      default: return status;
    }
  };

  const renderRequest = ({ item }: { item: WalletRequest }) => (
    <View style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.users.name}</Text>
          <Text style={styles.userEmail}>{item.users.email}</Text>
        </View>
        <View style={[
          styles.statusBadge,
          { backgroundColor: getStatusColor(item.status) }
        ]}>
          <Text style={styles.statusText}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.requestDetails}>
        <View style={styles.amountContainer}>
          <Text style={styles.amount}>{formatPrice(item.amount, item.currency)}</Text>
          <Text style={styles.paymentMethod}>
            {item.payment_method === 'bank_transfer' ? 'Banka Havalesi' : item.payment_method}
          </Text>
        </View>
        <Text style={styles.requestDate}>
          {new Date(item.created_at).toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
      </View>

      {item.status === 'pending' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.rejectButton}
            onPress={() => handleReject(item)}
            disabled={updateRequestMutation.isPending}
          >
            <Ionicons name="close" size={16} color="white" />
            <Text style={styles.rejectButtonText}>Reddet</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.approveButton}
            onPress={() => handleApprove(item)}
            disabled={updateRequestMutation.isPending}
          >
            <Ionicons name="checkmark" size={16} color="white" />
            <Text style={styles.approveButtonText}>Onayla</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Para Yükleme Talepleri',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />

      <View style={styles.container}>
        {/* Filter Tabs */}
        <View style={styles.filterTabs}>
          {[
            { key: 'pending', label: 'Beklemede', count: requests?.filter(r => r.status === 'pending').length || 0 },
            { key: 'approved', label: 'Onaylanan', count: requests?.filter(r => r.status === 'approved').length || 0 },
            { key: 'rejected', label: 'Reddedilen', count: requests?.filter(r => r.status === 'rejected').length || 0 },
            { key: 'all', label: 'Tümü', count: requests?.length || 0 },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.filterTab,
                filter === tab.key && styles.filterTabActive
              ]}
              onPress={() => setFilter(tab.key as any)}
            >
              <Text style={[
                styles.filterTabText,
                filter === tab.key && styles.filterTabTextActive
              ]}>
                {tab.label} ({tab.count})
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Requests List */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Talepler yükleniyor...</Text>
          </View>
        ) : (
          <FlatList
            data={requests}
            renderItem={renderRequest}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Ionicons name="wallet-outline" size={48} color="#ccc" />
                <Text style={styles.emptyText}>
                  {filter === 'pending' ? 'Bekleyen talep bulunmuyor' : 'Talep bulunamadı'}
                </Text>
              </View>
            }
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  filterTabs: {
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  filterTabActive: {
    backgroundColor: '#007AFF',
  },
  filterTabText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  filterTabTextActive: {
    color: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    padding: 16,
  },
  requestCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  requestDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  amountContainer: {
    flex: 1,
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  paymentMethod: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  requestDate: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#FF3B30',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginRight: 8,
  },
  rejectButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  approveButton: {
    flex: 1,
    backgroundColor: '#34C759',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 8,
  },
  approveButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center',
  },
});
