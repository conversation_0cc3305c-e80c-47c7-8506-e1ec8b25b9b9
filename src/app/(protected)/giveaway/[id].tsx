import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Dimensions,
  FlatList,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

const { width } = Dimensions.get('window');

interface Giveaway {
  id: string;
  title_tr: string;
  description_tr: string;
  image: string;
  rules_tr?: string;
  ticket_price: number;
  currency: string;
  total_tickets: number;
  tickets_sold: number;
  max_tickets_per_user: number;
  status: string;
  start_date: string;
  end_date: string;
  draw_date?: string;
}

interface GiveawayPrize {
  id: number;
  rank: number;
  title_tr: string;
  description_tr?: string;
  value: number;
  currency: string;
  image?: string;
  prize_type: string;
}

interface GiveawayTicket {
  id: number;
  ticket_number: string;
  purchased_at: string;
  status: string;
}

export default function GiveawayDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [ticketCount, setTicketCount] = useState('1');
  const [timeRemaining, setTimeRemaining] = useState('');
  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Çekiliş detayını getir
  const { data: giveaway, isLoading, error } = useQuery({
    queryKey: ['giveaway', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('giveaways')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Giveaway;
    },
  });

  // Çekiliş ödüllerini getir
  const { data: prizes } = useQuery({
    queryKey: ['giveaway-prizes', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('giveaway_prizes')
        .select('*')
        .eq('giveaway_id', id)
        .order('rank');

      if (error) throw error;
      return data as GiveawayPrize[];
    },
  });

  // Kullanıcının biletlerini getir
  const { data: userTickets } = useQuery({
    queryKey: ['user-giveaway-tickets', id, user?.id],
    queryFn: async () => {
      if (!user?.id) return [];

      const { data, error } = await supabase
        .from('giveaway_tickets')
        .select('*')
        .eq('giveaway_id', id)
        .eq('user_id', user.id)
        .order('purchased_at', { ascending: false });

      if (error) throw error;
      return data as GiveawayTicket[];
    },
    enabled: !!user?.id,
  });

  // Bilet satın alma mutation
  const buyTicketsMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');
      if (!ticketCount || parseInt(ticketCount) <= 0) {
        throw new Error('Geçerli bir bilet sayısı giriniz');
      }

      const count = parseInt(ticketCount);
      const userTicketCount = userTickets?.length || 0;
      
      if (userTicketCount + count > (giveaway?.max_tickets_per_user || 10)) {
        throw new Error(`Maksimum ${giveaway?.max_tickets_per_user} bilet alabilirsiniz`);
      }

      if ((giveaway?.tickets_sold || 0) + count > (giveaway?.total_tickets || 0)) {
        throw new Error('Yeterli bilet kalmamış');
      }

      // Önce katılımcı olarak ekle
      await supabase
        .from('giveaway_participants')
        .upsert({
          giveaway_id: id,
          user_id: user.id,
        });

      // Biletleri oluştur
      const tickets = [];
      for (let i = 0; i < count; i++) {
        const ticketNumber = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        tickets.push({
          giveaway_id: id,
          user_id: user.id,
          ticket_number: ticketNumber,
          chosen_digit_count: 3,
        });
      }

      const { data, error } = await supabase
        .from('giveaway_tickets')
        .insert(tickets)
        .select();

      if (error) throw error;

      // Çekilişin satılan bilet sayısını güncelle
      await supabase
        .from('giveaways')
        .update({ 
          tickets_sold: (giveaway?.tickets_sold || 0) + count 
        })
        .eq('id', id);

      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', `${ticketCount} bilet başarıyla satın alındı!`);
      setTicketCount('1');
      queryClient.invalidateQueries({ queryKey: ['giveaway', id] });
      queryClient.invalidateQueries({ queryKey: ['user-giveaway-tickets', id, user?.id] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Bilet satın alınırken hata oluştu');
    },
  });

  // Zaman sayacı
  useEffect(() => {
    if (!giveaway) return;

    const updateTimeRemaining = () => {
      const end = new Date(giveaway.end_date);
      const now = new Date();
      const diff = end.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining('Süresi doldu');
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeRemaining(`${days} gün ${hours} saat`);
      } else if (hours > 0) {
        setTimeRemaining(`${hours} saat ${minutes} dakika`);
      } else {
        setTimeRemaining(`${minutes} dakika`);
      }
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 60000); // Her dakika güncelle

    return () => clearInterval(interval);
  }, [giveaway]);

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const calculateProgress = () => {
    if (!giveaway) return 0;
    return (giveaway.tickets_sold / giveaway.total_tickets) * 100;
  };

  const renderPrize = ({ item }: { item: GiveawayPrize }) => (
    <View style={styles.prizeItem}>
      <View style={styles.prizeRank}>
        <Text style={styles.prizeRankText}>{item.rank}</Text>
      </View>
      
      {item.image && (
        <Image source={{ uri: item.image }} style={styles.prizeImage} />
      )}
      
      <View style={styles.prizeInfo}>
        <Text style={styles.prizeTitle}>{item.title_tr}</Text>
        {item.description_tr && (
          <Text style={styles.prizeDescription}>{item.description_tr}</Text>
        )}
        <Text style={styles.prizeValue}>
          {formatPrice(item.value, item.currency)}
        </Text>
      </View>
    </View>
  );

  const renderTicket = ({ item }: { item: GiveawayTicket }) => (
    <View style={[styles.ticketItem, item.status === 'won' && styles.winningTicket]}>
      <Text style={styles.ticketNumber}>#{item.ticket_number}</Text>
      <Text style={styles.ticketDate}>
        {new Date(item.purchased_at).toLocaleDateString('tr-TR')}
      </Text>
      {item.status === 'won' && (
        <Ionicons name="trophy" size={16} color="#FFD700" />
      )}
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Çekiliş yükleniyor...</Text>
      </View>
    );
  }

  if (error || !giveaway) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Çekiliş bulunamadı</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isActive = giveaway.status === 'active';
  const progress = calculateProgress();
  const userTicketCount = userTickets?.length || 0;
  const maxTickets = giveaway.max_tickets_per_user - userTicketCount;

  return (
    <>
      <Stack.Screen
        options={{
          title: giveaway.title_tr,
        }}
      />
      
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Çekiliş Resmi */}
        <Image 
          source={{ uri: giveaway.image || 'https://via.placeholder.com/400' }} 
          style={styles.giveawayImage} 
        />

        {/* Çekiliş Bilgileri */}
        <View style={styles.giveawayInfo}>
          <Text style={styles.giveawayTitle}>{giveaway.title_tr}</Text>
          
          <Text style={styles.giveawayDescription}>
            {giveaway.description_tr}
          </Text>

          {/* Progress Bar */}
          <View style={styles.progressSection}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressLabel}>Satılan Biletler</Text>
              <Text style={styles.progressText}>
                {giveaway.tickets_sold}/{giveaway.total_tickets} (%{progress.toFixed(1)})
              </Text>
            </View>
            <View style={styles.progressBar}>
              <View 
                style={[styles.progressFill, { width: `${progress}%` }]} 
              />
            </View>
          </View>

          {/* Bilet Bilgileri */}
          <View style={styles.ticketInfoSection}>
            <View style={styles.ticketInfoItem}>
              <Text style={styles.ticketInfoLabel}>Bilet Fiyatı</Text>
              <Text style={styles.ticketInfoValue}>
                {formatPrice(giveaway.ticket_price, giveaway.currency)}
              </Text>
            </View>
            
            <View style={styles.ticketInfoItem}>
              <Text style={styles.ticketInfoLabel}>Kalan Süre</Text>
              <Text style={[
                styles.ticketInfoValue,
                { color: isActive ? '#4CAF50' : '#666' }
              ]}>
                {timeRemaining}
              </Text>
            </View>
          </View>

          {/* Bilet Satın Alma */}
          {isActive && user && maxTickets > 0 && (
            <View style={styles.buyTicketSection}>
              <Text style={styles.buyTicketTitle}>Bilet Satın Al</Text>
              <Text style={styles.maxTicketText}>
                Maksimum {maxTickets} bilet alabilirsiniz
              </Text>
              
              <View style={styles.ticketCountContainer}>
                <Text style={styles.ticketCountLabel}>Bilet Sayısı:</Text>
                <TextInput
                  style={styles.ticketCountInput}
                  value={ticketCount}
                  onChangeText={setTicketCount}
                  keyboardType="numeric"
                  maxLength={2}
                />
              </View>
              
              <View style={styles.totalPriceContainer}>
                <Text style={styles.totalPriceLabel}>Toplam:</Text>
                <Text style={styles.totalPriceValue}>
                  {formatPrice(
                    giveaway.ticket_price * (parseInt(ticketCount) || 0), 
                    giveaway.currency
                  )}
                </Text>
              </View>
              
              <TouchableOpacity
                style={styles.buyButton}
                onPress={() => buyTicketsMutation.mutate()}
                disabled={buyTicketsMutation.isPending}
              >
                {buyTicketsMutation.isPending ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Ionicons name="ticket-outline" size={20} color="white" />
                    <Text style={styles.buyButtonText}>Bilet Satın Al</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          )}

          {/* Kullanıcının Biletleri */}
          {userTickets && userTickets.length > 0 && (
            <View style={styles.userTicketsSection}>
              <Text style={styles.userTicketsTitle}>
                Biletlerim ({userTickets.length})
              </Text>
              <FlatList
                data={userTickets}
                renderItem={renderTicket}
                keyExtractor={(item) => item.id.toString()}
                numColumns={3}
                scrollEnabled={false}
                columnWrapperStyle={styles.ticketRow}
              />
            </View>
          )}

          {/* Ödüller */}
          {prizes && prizes.length > 0 && (
            <View style={styles.prizesSection}>
              <Text style={styles.prizesSectionTitle}>Ödüller</Text>
              <FlatList
                data={prizes}
                renderItem={renderPrize}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            </View>
          )}

          {/* Kurallar */}
          {giveaway.rules_tr && (
            <View style={styles.rulesSection}>
              <Text style={styles.rulesSectionTitle}>Çekiliş Kuralları</Text>
              <Text style={styles.rulesText}>{giveaway.rules_tr}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  giveawayImage: {
    width: width,
    height: width * 0.6,
    backgroundColor: '#f0f0f0',
  },
  giveawayInfo: {
    backgroundColor: 'white',
    padding: 20,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  giveawayTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  giveawayDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  progressSection: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  ticketInfoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  ticketInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  ticketInfoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  ticketInfoValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  buyTicketSection: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  buyTicketTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  maxTicketText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  ticketCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ticketCountLabel: {
    fontSize: 16,
    color: '#333',
    marginRight: 12,
  },
  ticketCountInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 8,
    fontSize: 16,
    backgroundColor: 'white',
    width: 60,
    textAlign: 'center',
  },
  totalPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalPriceLabel: {
    fontSize: 16,
    color: '#666',
  },
  totalPriceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  buyButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  buyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  userTicketsSection: {
    marginBottom: 20,
  },
  userTicketsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  ticketRow: {
    justifyContent: 'space-between',
  },
  ticketItem: {
    backgroundColor: '#f0f8ff',
    padding: 8,
    borderRadius: 8,
    marginBottom: 8,
    width: (width - 80) / 3,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  winningTicket: {
    backgroundColor: '#fff8e1',
    borderColor: '#FFD700',
  },
  ticketNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  ticketDate: {
    fontSize: 10,
    color: '#666',
  },
  prizesSection: {
    marginBottom: 20,
  },
  prizesSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  prizeItem: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  prizeRank: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FFD700',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  prizeRankText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'white',
  },
  prizeImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  prizeInfo: {
    flex: 1,
  },
  prizeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  prizeDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  prizeValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  rulesSection: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
  },
  rulesSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  rulesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});
