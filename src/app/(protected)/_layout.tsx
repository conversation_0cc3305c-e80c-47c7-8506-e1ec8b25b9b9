import { Redirect, Stack } from 'expo-router'
import { useAuth } from '@clerk/clerk-expo'
import { useUser } from '../../hooks/useUser'

export default function AppLayout() {
  const { isSignedIn } = useAuth()
  useUser() // User sync'i başlat

  if (!isSignedIn) {
    return <Redirect href={'/signIn'} />
  }

  return (
    <Stack>
      <Stack.Screen name='(tabs)' options={{ headerShown: false }} />
      <Stack.Screen
        name='product/[id]'
        options={{
          headerTitle: 'Ürün Detayı',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='auction/[id]'
        options={{
          headerTitle: 'Açık Artırma',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='giveaway/[id]'
        options={{
          headerTitle: 'Çekiliş',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/dashboard'
        options={{
          headerTitle: 'Admin Dashboard',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/products'
        options={{
          headerTitle: '<PERSON>rün Yönetimi',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/categories'
        options={{
          headerTitle: 'Kategori Yönetimi',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/giveaways'
        options={{
          headerTitle: 'Çekiliş Yönetimi',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/auctions'
        options={{
          headerTitle: 'Açık Artırma Yönetimi',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='debug'
        options={{
          headerTitle: 'Debug',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='wallet'
        options={{
          headerTitle: 'Cüzdanım',
          headerBackTitle: 'Geri'
        }}
      />
      <Stack.Screen
        name='admin/wallet-requests'
        options={{
          headerTitle: 'Cüzdan Talepleri',
          headerBackTitle: 'Geri'
        }}
      />
    </Stack>
  )
}