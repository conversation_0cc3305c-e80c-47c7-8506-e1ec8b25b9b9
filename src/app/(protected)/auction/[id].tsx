import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Dimensions,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { useUser } from '@clerk/clerk-expo';

const { width } = Dimensions.get('window');

interface Auction {
  id: string;
  product_id?: string;
  description_tr: string;
  description_en?: string;
  image: string;
  starting_bid: number;
  current_bid: number;
  bid_increment_amount: number;
  currency: string;
  start_time: string;
  end_time: string;
  status: string;
  products?: {
    id: string;
    name_tr: string;
    image: string;
  };
}

interface AuctionBid {
  id: number;
  bidder_id: string;
  bid_amount: number;
  bid_time: string;
  is_winning_bid: boolean;
  users?: {
    name: string;
  };
}

export default function AuctionDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [bidAmount, setBidAmount] = useState('');
  const [timeRemaining, setTimeRemaining] = useState('');
  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Açık artırma detayını getir
  const { data: auction, isLoading, error } = useQuery({
    queryKey: ['auction', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('auctions')
        .select(`
          *,
          products(id, name_tr, image)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data as Auction;
    },
  });

  // Açık artırma tekliflerini getir
  const { data: bids, isLoading: bidsLoading } = useQuery({
    queryKey: ['auction-bids', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('auction_bids')
        .select(`
          *,
          users(name)
        `)
        .eq('auction_id', id)
        .order('bid_amount', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data as AuctionBid[];
    },
  });

  // Teklif verme mutation
  const placeBidMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');
      if (!bidAmount || parseFloat(bidAmount) <= 0) {
        throw new Error('Geçerli bir teklif tutarı giriniz');
      }

      const amount = parseFloat(bidAmount);
      const minBid = (auction?.current_bid || auction?.starting_bid || 0) + (auction?.bid_increment_amount || 0);
      
      if (amount < minBid) {
        throw new Error(`Minimum teklif tutarı: ${formatPrice(minBid, auction?.currency || 'TRY')}`);
      }

      // Önce katılımcı olarak ekle
      await supabase
        .from('auction_bidders')
        .upsert({
          auction_id: id,
          bidder_id: user.id,
        });

      // Teklifi ekle
      const { data, error } = await supabase
        .from('auction_bids')
        .insert({
          auction_id: id,
          bidder_id: user.id,
          bid_amount: amount,
          is_winning_bid: true,
        })
        .select();

      if (error) throw error;

      // Açık artırmanın mevcut teklifini güncelle
      await supabase
        .from('auctions')
        .update({ current_bid: amount })
        .eq('id', id);

      // Diğer tüm teklifleri kazanmayan yap
      await supabase
        .from('auction_bids')
        .update({ is_winning_bid: false })
        .eq('auction_id', id)
        .neq('bidder_id', user.id);

      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Teklifiniz başarıyla verildi!');
      setBidAmount('');
      queryClient.invalidateQueries({ queryKey: ['auction', id] });
      queryClient.invalidateQueries({ queryKey: ['auction-bids', id] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Teklif verilirken hata oluştu');
    },
  });

  // Zaman sayacı
  useEffect(() => {
    if (!auction) return;

    const updateTimeRemaining = () => {
      const end = new Date(auction.end_time);
      const now = new Date();
      const diff = end.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining('Süresi doldu');
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      if (days > 0) {
        setTimeRemaining(`${days}g ${hours}s ${minutes}d`);
      } else if (hours > 0) {
        setTimeRemaining(`${hours}s ${minutes}d ${seconds}s`);
      } else {
        setTimeRemaining(`${minutes}d ${seconds}s`);
      }
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 1000);

    return () => clearInterval(interval);
  }, [auction]);

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const getMinimumBid = () => {
    if (!auction) return 0;
    return (auction.current_bid || auction.starting_bid) + auction.bid_increment_amount;
  };

  const renderBid = ({ item }: { item: AuctionBid }) => (
    <View style={[styles.bidItem, item.is_winning_bid && styles.winningBid]}>
      <View style={styles.bidHeader}>
        <Text style={styles.bidderName}>
          {item.users?.name || 'Anonim'}
          {item.is_winning_bid && ' 🏆'}
        </Text>
        <Text style={styles.bidAmount}>
          {formatPrice(item.bid_amount, auction?.currency || 'TRY')}
        </Text>
      </View>
      <Text style={styles.bidTime}>
        {new Date(item.bid_time).toLocaleDateString('tr-TR')} {new Date(item.bid_time).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Açık artırma yükleniyor...</Text>
      </View>
    );
  }

  if (error || !auction) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Açık artırma bulunamadı</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isActive = auction.status === 'active';
  const minimumBid = getMinimumBid();

  return (
    <>
      <Stack.Screen
        options={{
          title: auction.products?.name_tr || `Açık Artırma ${auction.id.slice(-6)}`,
        }}
      />
      
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Ürün Resmi */}
        <Image 
          source={{ uri: auction.products?.image || auction.image || 'https://via.placeholder.com/400' }} 
          style={styles.auctionImage} 
        />

        {/* Açık Artırma Bilgileri */}
        <View style={styles.auctionInfo}>
          <Text style={styles.auctionTitle}>
            {auction.products?.name_tr || `Açık Artırma ${auction.id.slice(-6)}`}
          </Text>
          
          <Text style={styles.auctionDescription}>
            {auction.description_tr}
          </Text>

          {/* Teklif Bilgileri */}
          <View style={styles.bidInfoContainer}>
            <View style={styles.bidInfoItem}>
              <Text style={styles.bidInfoLabel}>Mevcut Teklif</Text>
              <Text style={styles.currentBidAmount}>
                {formatPrice(auction.current_bid || auction.starting_bid, auction.currency)}
              </Text>
            </View>
            
            <View style={styles.bidInfoItem}>
              <Text style={styles.bidInfoLabel}>Minimum Artış</Text>
              <Text style={styles.incrementAmount}>
                {formatPrice(auction.bid_increment_amount, auction.currency)}
              </Text>
            </View>
          </View>

          {/* Zaman Bilgisi */}
          <View style={styles.timeContainer}>
            <Ionicons 
              name="time-outline" 
              size={20} 
              color={isActive ? '#4CAF50' : '#666'} 
            />
            <Text style={[
              styles.timeText,
              { color: isActive ? '#4CAF50' : '#666' }
            ]}>
              {isActive ? `Kalan: ${timeRemaining}` : timeRemaining}
            </Text>
          </View>

          {/* Teklif Verme Formu */}
          {isActive && user && (
            <View style={styles.bidForm}>
              <Text style={styles.bidFormTitle}>Teklif Ver</Text>
              <Text style={styles.minimumBidText}>
                Minimum teklif: {formatPrice(minimumBid, auction.currency)}
              </Text>
              
              <TextInput
                style={styles.bidInput}
                placeholder={`Örn: ${minimumBid}`}
                value={bidAmount}
                onChangeText={setBidAmount}
                keyboardType="numeric"
              />
              
              <TouchableOpacity
                style={styles.bidButton}
                onPress={() => placeBidMutation.mutate()}
                disabled={placeBidMutation.isPending}
              >
                {placeBidMutation.isPending ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Ionicons name="hammer-outline" size={20} color="white" />
                    <Text style={styles.bidButtonText}>Teklif Ver</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          )}

          {/* Teklif Geçmişi */}
          <View style={styles.bidsSection}>
            <Text style={styles.bidsSectionTitle}>
              Teklif Geçmişi ({bids?.length || 0})
            </Text>
            
            {bidsLoading ? (
              <ActivityIndicator size="small" color="#007AFF" style={styles.bidsLoader} />
            ) : bids && bids.length > 0 ? (
              bids.map((bid, index) => (
                <View key={bid.id}>
                  {renderBid({ item: bid })}
                </View>
              ))
            ) : (
              <Text style={styles.noBidsText}>Henüz teklif verilmemiş</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  auctionImage: {
    width: width,
    height: width * 0.8,
    backgroundColor: '#f0f0f0',
  },
  auctionInfo: {
    backgroundColor: 'white',
    padding: 20,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  auctionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  auctionDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  bidInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  bidInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  bidInfoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  currentBidAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  incrementAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF9800',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bidForm: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  bidFormTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  minimumBidText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  bidInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
    marginBottom: 12,
  },
  bidButton: {
    backgroundColor: '#FF9800',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  bidButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bidsSection: {
    marginTop: 20,
  },
  bidsSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  bidsLoader: {
    marginVertical: 20,
  },
  noBidsText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginVertical: 20,
  },
  bidItem: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#e0e0e0',
  },
  winningBid: {
    borderLeftColor: '#4CAF50',
    backgroundColor: '#f8fff8',
  },
  bidHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  bidderName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  bidAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  bidTime: {
    fontSize: 12,
    color: '#666',
  },
});
