import { Tabs } from "expo-router";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import { useAuth } from "@clerk/clerk-expo";
import React, { useState } from 'react';
import { View, TouchableOpacity, Image, Text, StyleSheet, Modal, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { router } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';

// UserProfileDropdown component
function UserProfileDropdown() {
  const [isVisible, setIsVisible] = useState(false);
  const { user } = useUser();
  const { signOut } = useAuth();
  const supabase = useSupabase();

  // Kullanıcı cüzdan bilgilerini getir
  const { data: userWallet } = useQuery({
    queryKey: ['user-wallet', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('users')
        .select('wallet_balance, currency, loyalty_points, role')
        .eq('clerk_id', user.id)
        .single();

      if (error) return null;
      return data;
    },
    enabled: !!user?.id,
  });

  // Sepet item sayısını getir
  const { data: cartCount } = useQuery({
    queryKey: ['cart-count', user?.id],
    queryFn: async () => {
      if (!user?.id) return 0;

      const { data, error } = await supabase
        .from('cart_items')
        .select('quantity')
        .eq('user_id', user.id);

      if (error) return 0;
      return data.reduce((total, item) => total + item.quantity, 0);
    },
    enabled: !!user?.id,
  });

  const formatPrice = (price: number, currency: string = 'TRY') => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const handleSignOut = () => {
    Alert.alert(
      'Çıkış Yap',
      'Çıkış yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: () => signOut()
        }
      ]
    );
  };

  if (!user) return null;

  return (
    <>
      <TouchableOpacity
        style={styles.profileButton}
        onPress={() => setIsVisible(true)}
      >
        <Image
          source={{
            uri: user.imageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName || 'User')}&background=007AFF&color=fff&size=64`
          }}
          style={styles.profileAvatar}
        />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsVisible(false)}
        >
          <View style={styles.dropdown}>
            {/* Kullanıcı Bilgileri */}
            <View style={styles.userInfo}>
              <Image
                source={{
                  uri: user.imageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName || 'User')}&background=007AFF&color=fff&size=100`
                }}
                style={styles.dropdownAvatar}
              />
              <View style={styles.userDetails}>
                <Text style={styles.userName}>
                  {user.fullName || user.firstName || 'Kullanıcı'}
                </Text>
                <Text style={styles.userEmail}>
                  {user.primaryEmailAddress?.emailAddress}
                </Text>
              </View>
            </View>

            <View style={styles.divider} />

            {/* Cüzdan Bilgisi */}
            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={() => {
                setIsVisible(false);
                router.push('/wallet');
              }}
            >
              <Ionicons name="wallet-outline" size={20} color="#007AFF" />
              <View style={styles.itemContent}>
                <Text style={styles.itemTitle}>Cüzdanım</Text>
                <Text style={styles.itemSubtitle}>
                  {userWallet ? formatPrice(userWallet.wallet_balance || 0, userWallet.currency || 'TRY') : '₺0.00'}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#666" />
            </TouchableOpacity>

            {/* Sepet Bilgisi */}
            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={() => {
                setIsVisible(false);
                router.push('/cart');
              }}
            >
              <Ionicons name="cart-outline" size={20} color="#007AFF" />
              <View style={styles.itemContent}>
                <Text style={styles.itemTitle}>Sepetim</Text>
                <Text style={styles.itemSubtitle}>
                  {cartCount || 0} ürün
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={16} color="#666" />
            </TouchableOpacity>

            {/* Sadakat Puanları */}
            {userWallet?.loyalty_points && (
              <View style={styles.dropdownItem}>
                <Ionicons name="star-outline" size={20} color="#FFD700" />
                <View style={styles.itemContent}>
                  <Text style={styles.itemTitle}>Sadakat Puanları</Text>
                  <Text style={styles.itemSubtitle}>
                    {userWallet.loyalty_points} puan
                  </Text>
                </View>
              </View>
            )}

            {/* Admin Dashboard (sadece adminler için) */}
            {userWallet?.role === 'admin' && (
              <>
                <TouchableOpacity
                  style={styles.dropdownItem}
                  onPress={() => {
                    setIsVisible(false);
                    router.push('/admin/dashboard');
                  }}
                >
                  <Ionicons name="settings-outline" size={20} color="#FF3B30" />
                  <View style={styles.itemContent}>
                    <Text style={styles.itemTitle}>Admin Dashboard</Text>
                    <Text style={styles.itemSubtitle}>Yönetim paneli</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={16} color="#666" />
                </TouchableOpacity>

                <View style={styles.divider} />
              </>
            )}

            {/* Çıkış Yap */}
            <TouchableOpacity
              style={styles.dropdownItem}
              onPress={handleSignOut}
            >
              <Ionicons name="log-out-outline" size={20} color="#FF3B30" />
              <View style={styles.itemContent}>
                <Text style={[styles.itemTitle, { color: '#FF3B30' }]}>Çıkış Yap</Text>
              </View>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#007AFF',
        headerRight: () => <UserProfileDropdown />
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Ana Sayfa',
          headerTitle: 'E-Ticaret',
          headerTintColor: "#007AFF",
          tabBarIcon: ({ color }) => <AntDesign name="home" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="products"
        options={{
          title: 'Ürünler',
          headerTitle: 'Tüm Ürünler',
          tabBarIcon: ({ color }) => <Ionicons name="storefront-outline" size={24} color={color} />
        }}
      />

      <Tabs.Screen
        name="giveaways"
        options={{
          title: 'Çekilişler',
          headerTitle: 'Çekilişler',
          tabBarIcon: ({ color }) => <Ionicons name="gift-outline" size={24} color={color} />
        }}
      />
      <Tabs.Screen
        name="auctions"
        options={{
          title: 'Açık Artırma',
          headerTitle: 'Açık Artırmalar',
          tabBarIcon: ({ color }) => <Ionicons name="hammer-outline" size={24} color={color} />
        }}
      />
            <Tabs.Screen
        name="cart"
        options={{
          title: 'Sepet',
          headerTitle: 'Sepetim',
          tabBarIcon: ({ color }) => <Ionicons name="cart-outline" size={24} color={color} />
        }}
      />
      <Tabs.Screen
        name="wallet"
        options={{
          title: 'Cüzdan',
          headerTitle: 'Cüzdanım',
          tabBarIcon: ({ color }) => <Ionicons name="wallet-outline" size={24} color={color} />
        }}
      />
    </Tabs>
  )
}

const styles = StyleSheet.create({
  profileButton: {
    marginRight: 15,
    padding: 2,
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 90,
    paddingRight: 15,
  },
  dropdown: {
    backgroundColor: 'white',
    borderRadius: 12,
    minWidth: 280,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  dropdownAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 16,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  itemContent: {
    flex: 1,
    marginLeft: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    color: '#666',
  },
});