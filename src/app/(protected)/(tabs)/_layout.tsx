import { Tabs } from "expo-router";
import { AntDesign, Feather, Ionicons } from "@expo/vector-icons";
import { useAuth } from "@clerk/clerk-expo";

export default function TabLayout() {
  const { signOut } = useAuth()
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#007AFF',
        headerRight: () =>
          <Feather
            name="log-out"
            size={22}
            color="black"
            style={{ paddingRight: 10 }}
            onPress={() => signOut()}
          />
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Ana Sayfa',
          headerTitle: 'E-Ticaret',
          headerTintColor: "#007AFF",
          tabBarIcon: ({ color }) => <AntDesign name="home" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="products"
        options={{
          title: 'Ürünler',
          headerTitle: 'Tüm <PERSON>ün<PERSON>',
          tabBarIcon: ({ color }) => <Ionicons name="storefront-outline" size={24} color={color} />
        }}
      />
      <Tabs.Screen
        name="cart"
        options={{
          title: 'Sepet',
          headerTitle: 'Sepetim',
          tabBarIcon: ({ color }) => <Ionicons name="cart-outline" size={24} color={color} />
        }}
      />
      <Tabs.Screen
        name="giveaways"
        options={{
          title: 'Çekilişler',
          headerTitle: 'Çekilişler',
          tabBarIcon: ({ color }) => <Ionicons name="gift-outline" size={24} color={color} />
        }}
      />
      <Tabs.Screen
        name="auctions"
        options={{
          title: 'Açık Artırma',
          headerTitle: 'Açık Artırmalar',
          tabBarIcon: ({ color }) => <Ionicons name="hammer-outline" size={24} color={color} />
        }}
      />
    </Tabs>
  )
}