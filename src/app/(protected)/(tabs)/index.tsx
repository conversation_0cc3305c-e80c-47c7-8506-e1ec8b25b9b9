import React from "react";
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Image,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from "react-native";
import { useQuery } from "@tanstack/react-query";
import { useSupabase } from "../../../lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useUser } from "@clerk/clerk-expo";

const { width } = Dimensions.get('window');

interface Product {
  id: string;
  name_tr: string;
  name_en: string;
  description_tr: string;
  image: string;
  price: number;
  currency: string;
  stock: number;
  is_available: boolean;
}

interface Giveaway {
  id: string;
  title_tr: string;
  description_tr: string;
  image: string;
  ticket_price: number;
  currency: string;
  total_tickets: number;
  tickets_sold: number;
  status: string;
  end_date: string;
}

interface Auction {
  id: string;
  description_tr: string;
  image: string;
  starting_bid: number;
  current_bid: number;
  currency: string;
  end_time: string;
  status: string;
  products?: {
    name_tr: string;
    image: string;
  };
}

interface Category {
  id: string;
  name_tr: string;
  name_en: string;
  image: string;
}

export default function HomeScreen() {
  const supabase = useSupabase();
  const { user } = useUser();

  // Kategorileri getir
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name_tr')
        .limit(5);
      
      if (error) throw error;
      return data as Category[];
    },
  });

  // Öne çıkan ürünleri getir (en pahalı 4 ürün)
  const { data: featuredProducts, isLoading: productsLoading } = useQuery({
    queryKey: ['featured-products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_available', true)
        .order('price', { ascending: false })
        .limit(4);

      if (error) throw error;
      return data as Product[];
    },
  });

  // Öne çıkan çekilişleri getir
  const { data: featuredGiveaways, isLoading: giveawaysLoading } = useQuery({
    queryKey: ['featured-giveaways'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('giveaways')
        .select('*')
        .eq('status', 'active')
        .limit(3);

      if (error) throw error;
      return data as Giveaway[];
    },
  });

  // Öne çıkan açık artırmaları getir
  const { data: featuredAuctions, isLoading: auctionsLoading } = useQuery({
    queryKey: ['featured-auctions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('auctions')
        .select(`
          *,
          products(name_tr, image)
        `)
        .eq('status', 'active')
        .limit(3);

      if (error) throw error;
      return data as Auction[];
    },
  });

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => router.push('/products')}
    >
      <Image source={{ uri: item.image }} style={styles.categoryImage} />
      <Text style={styles.categoryName}>{item.name_tr}</Text>
    </TouchableOpacity>
  );

  const renderFeaturedProduct = ({ item }: { item: Product }) => (
    <TouchableOpacity
      style={styles.productCard}
      onPress={() => router.push(`/product/${item.id}`)}
    >
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>{item.name_tr}</Text>
        <Text style={styles.productPrice}>{formatPrice(item.price, item.currency)}</Text>
        <View style={styles.stockContainer}>
          <Ionicons name="cube-outline" size={12} color="#666" />
          <Text style={styles.stockText}>Stok: {item.stock}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderFeaturedGiveaway = ({ item }: { item: Giveaway }) => {
    const progress = (item.tickets_sold / item.total_tickets) * 100;
    const endDate = new Date(item.end_date);
    const now = new Date();
    const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return (
      <TouchableOpacity
        style={styles.giveawayCard}
        onPress={() => router.push(`/giveaway/${item.id}`)}
      >
        <Image source={{ uri: item.image }} style={styles.giveawayImage} />
        <View style={styles.giveawayInfo}>
          <Text style={styles.giveawayTitle} numberOfLines={2}>{item.title_tr}</Text>
          <Text style={styles.giveawayPrice}>{formatPrice(item.ticket_price, item.currency)}</Text>
          <View style={styles.giveawayProgress}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.progressText}>{progress.toFixed(0)}%</Text>
          </View>
          <Text style={styles.giveawayDays}>{daysLeft > 0 ? `${daysLeft} gün kaldı` : 'Süresi doldu'}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFeaturedAuction = ({ item }: { item: Auction }) => {
    const endDate = new Date(item.end_time);
    const now = new Date();
    const hoursLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60));

    return (
      <TouchableOpacity
        style={styles.auctionCard}
        onPress={() => router.push(`/auction/${item.id}`)}
      >
        <Image
          source={{ uri: item.products?.image || item.image }}
          style={styles.auctionImage}
        />
        <View style={styles.auctionInfo}>
          <Text style={styles.auctionTitle} numberOfLines={2}>
            {item.products?.name_tr || `Açık Artırma ${item.id.slice(-6)}`}
          </Text>
          <Text style={styles.auctionPrice}>
            {formatPrice(item.current_bid || item.starting_bid, item.currency)}
          </Text>
          <Text style={styles.auctionTime}>
            {hoursLeft > 0 ? `${hoursLeft} saat kaldı` : 'Süresi doldu'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (categoriesLoading || productsLoading || giveawaysLoading || auctionsLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Hero Banner */}
      <View style={styles.heroBanner}>
        <Image
          source={{ uri: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800' }}
          style={styles.bannerImage}
        />
        <View style={styles.bannerOverlay}>
          <View style={styles.bannerContent}>
            <Text style={styles.bannerTitle}>Hoş Geldiniz{user?.firstName ? `, ${user.firstName}!` : '!'}</Text>
            <Text style={styles.bannerSubtitle}>Özel ürünler, çekilişler ve açık artırmalar sizi bekliyor</Text>
            <View style={styles.bannerButtons}>
              <TouchableOpacity
                style={styles.bannerButton}
                onPress={() => router.push('/products')}
              >
                <Text style={styles.bannerButtonText}>Alışveriş</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.bannerButton, styles.bannerButtonSecondary]}
                onPress={() => router.push('/giveaways')}
              >
                <Text style={[styles.bannerButtonText, styles.bannerButtonTextSecondary]}>Çekilişler</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.bannerButton, styles.bannerButtonSecondary]}
                onPress={() => router.push('/auctions')}
              >
                <Text style={[styles.bannerButtonText, styles.bannerButtonTextSecondary]}>Açık Artırmalar</Text>
              </TouchableOpacity>
            </View>
          </View>
          {user && (
            <View style={styles.userProfile}>
              <Image
                source={{ uri: user.imageUrl || 'https://via.placeholder.com/40' }}
                style={styles.userAvatar}
              />
            </View>
          )}
        </View>
      </View>

      {/* Kategoriler */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>
          <TouchableOpacity onPress={() => router.push('/products')}>
            <Text style={styles.seeAllText}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />
      </View>

      {/* Öne Çıkan Çekilişler */}
      {featuredGiveaways && featuredGiveaways.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🎁 Aktif Çekilişler</Text>
            <TouchableOpacity onPress={() => router.push('/giveaways')}>
              <Text style={styles.seeAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={featuredGiveaways}
            renderItem={renderFeaturedGiveaway}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>
      )}

      {/* Öne Çıkan Açık Artırmalar */}
      {featuredAuctions && featuredAuctions.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🔨 Aktif Açık Artırmalar</Text>
            <TouchableOpacity onPress={() => router.push('/auctions')}>
              <Text style={styles.seeAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={featuredAuctions}
            renderItem={renderFeaturedAuction}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>
      )}

      {/* Öne Çıkan Ürünler */}
      {featuredProducts && featuredProducts.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>⭐ Premium Ürünler</Text>
            <TouchableOpacity onPress={() => router.push('/products')}>
              <Text style={styles.seeAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={featuredProducts}
            renderItem={renderFeaturedProduct}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.horizontalList}
          />
        </View>
      )}

      {/* Quick Actions 
     <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/wallet')}
        >
          <Ionicons name="wallet-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Cüzdan</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/cart')}
        >
          <Ionicons name="cart-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Sepet</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/giveaways')}
        >
          <Ionicons name="gift-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Çekilişler</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => router.push('/auctions')}
        >
          <Ionicons name="hammer-outline" size={24} color="#007AFF" />
          <Text style={styles.quickActionText}>Açık Artırma</Text>
        </TouchableOpacity>
     </View>*/}

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>© 2025 E-Ticaret Uygulaması</Text>
        <Text style={styles.footerSubtext}>Kaliteli alışverişin adresi</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  heroBanner: {
    height: 250,
    position: 'relative',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  bannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'space-between',
    padding: 20,
  },
  bannerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  bannerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 20,
    lineHeight: 22,
  },
  bannerButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  bannerButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  bannerButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: 'white',
  },
  bannerButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  bannerButtonTextSecondary: {
    color: 'white',
  },
  userProfile: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 3,
    borderColor: 'white',
  },
  section: {
    backgroundColor: "white",
    marginTop: 12,
    paddingVertical: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  seeAllText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "600",
  },
  categoriesList: {
    paddingHorizontal: 20,
  },
  categoryCard: {
    alignItems: "center",
    marginRight: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 16,
    width: 100,
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: "600",
    color: "#333",
    textAlign: "center",
  },
  productsList: {
    paddingHorizontal: 20,
  },
  productRow: {
    justifyContent: "space-between",
  },
  productCard: {
    backgroundColor: "white",
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    width: (width - 60) / 2,
  },
  productImage: {
    width: "100%",
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
    lineHeight: 18,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 4,
  },
  stockContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  stockText: {
    fontSize: 12,
    color: "#666",
    marginLeft: 4,
  },
  footer: {
    backgroundColor: "#333",
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: "center",
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: "white",
    fontWeight: "600",
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: "rgba(255,255,255,0.7)",
  },
  // Yeni stiller
  horizontalList: {
    paddingHorizontal: 20,
  },
  // Çekiliş kartları
  giveawayCard: {
    width: 200,
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  giveawayImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  giveawayInfo: {
    padding: 12,
  },
  giveawayTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  giveawayPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 8,
  },
  giveawayProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  giveawayDays: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '600',
  },
  // Açık artırma kartları
  auctionCard: {
    width: 200,
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  auctionImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  auctionInfo: {
    padding: 12,
  },
  auctionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  auctionPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9800',
    marginBottom: 4,
  },
  auctionTime: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  // Quick Actions
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 12,
  },
  quickActionText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
    marginTop: 4,
  },
});
