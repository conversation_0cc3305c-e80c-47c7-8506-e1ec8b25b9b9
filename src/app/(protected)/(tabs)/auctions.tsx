import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useSession } from '@clerk/clerk-expo';

const { width } = Dimensions.get('window');

interface Auction {
  id: string;
  product_id: string;
  description_tr: string;
  image: string;
  starting_bid: number;
  current_bid: number;
  bid_increment_amount: number;
  currency: string;
  start_time: string;
  end_time: string;
  status: string;
  products: {
    id: string;
    name_tr: string;
    image: string;
  };
}

export default function AuctionsScreen() {
  const [selectedStatus, setSelectedStatus] = useState<string>('active');
  const supabase = useSupabase();
  const { session } = useSession();
  const queryClient = useQueryClient();

  // Açık artırmaları getir
  const { data: auctions, isLoading, error } = useQuery({
    queryKey: ['auctions', selectedStatus],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('auctions')
        .select(`
          *,
          products!inner(id, name_tr, image)
        `)
        .eq('status', selectedStatus)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Auction[];
    },
  });

  // Açık artırmaya katılma mutation
  const joinAuctionMutation = useMutation({
    mutationFn: async (auctionId: string) => {
      if (!session?.user?.id) throw new Error('Giriş yapmanız gerekiyor');

      const { data, error } = await supabase
        .from('auction_bidders')
        .insert({
          auction_id: auctionId,
          bidder_id: session.user.id,
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Açık artırmaya katıldınız!');
      queryClient.invalidateQueries({ queryKey: ['auctions'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Açık artırmaya katılırken hata oluştu');
    },
  });

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'completed': return '#2196F3';
      case 'cancelled': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'pending': return 'Bekliyor';
      case 'completed': return 'Tamamlandı';
      case 'cancelled': return 'İptal';
      default: return status;
    }
  };

  const getTimeRemaining = (endTime: string) => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return 'Süresi doldu';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}g ${hours}s`;
    if (hours > 0) return `${hours}s ${minutes}d`;
    return `${minutes}d`;
  };

  const renderAuction = ({ item }: { item: Auction }) => {
    const isActive = item.status === 'active';
    const timeRemaining = getTimeRemaining(item.end_time);
    const currentBid = item.current_bid || item.starting_bid;
    const nextMinBid = currentBid + item.bid_increment_amount;

    return (
      <TouchableOpacity
        style={styles.auctionCard}
        onPress={() => router.push(`/auction/${item.id}`)}
      >
        <Image 
          source={{ uri: item.products?.image || item.image }} 
          style={styles.auctionImage} 
        />
        
        <View style={styles.auctionInfo}>
          <View style={styles.auctionHeader}>
            <Text style={styles.auctionTitle} numberOfLines={2}>
              {item.products?.name_tr || 'Ürün Adı'}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
            </View>
          </View>

          {item.description_tr && (
            <Text style={styles.auctionDescription} numberOfLines={2}>
              {item.description_tr}
            </Text>
          )}

          {/* Teklif Bilgileri */}
          <View style={styles.bidInfo}>
            <View style={styles.bidSection}>
              <Text style={styles.bidLabel}>Mevcut Teklif</Text>
              <Text style={styles.currentBid}>
                {formatPrice(currentBid, item.currency)}
              </Text>
            </View>
            
            <View style={styles.bidSection}>
              <Text style={styles.bidLabel}>Minimum Artış</Text>
              <Text style={styles.minBid}>
                {formatPrice(item.bid_increment_amount, item.currency)}
              </Text>
            </View>
          </View>

          {/* Sonraki Minimum Teklif */}
          <View style={styles.nextBidContainer}>
            <Text style={styles.nextBidLabel}>Sonraki minimum teklif:</Text>
            <Text style={styles.nextBidAmount}>
              {formatPrice(nextMinBid, item.currency)}
            </Text>
          </View>

          {/* Zaman Bilgisi */}
          <View style={styles.timeInfo}>
            <Ionicons 
              name="time-outline" 
              size={14} 
              color={isActive ? '#4CAF50' : '#666'} 
            />
            <Text style={[
              styles.timeText,
              { color: isActive ? '#4CAF50' : '#666' }
            ]}>
              {isActive ? `Kalan: ${timeRemaining}` : timeRemaining}
            </Text>
          </View>

          {/* Katılım Butonu */}
          {isActive && (
            <TouchableOpacity
              style={styles.joinButton}
              onPress={(e) => {
                e.stopPropagation();
                if (!session?.user?.id) {
                  Alert.alert('Giriş Gerekli', 'Açık artırmaya katılmak için giriş yapmanız gerekiyor');
                  return;
                }
                joinAuctionMutation.mutate(item.id);
              }}
              disabled={joinAuctionMutation.isPending}
            >
              {joinAuctionMutation.isPending ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <Ionicons name="hammer-outline" size={16} color="white" />
                  <Text style={styles.joinButtonText}>Teklif Ver</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const statusOptions = [
    { key: 'active', label: 'Aktif' },
    { key: 'pending', label: 'Bekliyor' },
    { key: 'completed', label: 'Tamamlandı' },
  ];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Açık artırmalar yükleniyor...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Açık artırmalar yüklenirken hata oluştu</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Status Filter */}
      <View style={styles.filterContainer}>
        {statusOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.filterButton,
              selectedStatus === option.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedStatus(option.key)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedStatus === option.key && styles.filterButtonTextActive
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Auctions List */}
      {auctions && auctions.length > 0 ? (
        <FlatList
          data={auctions}
          renderItem={renderAuction}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="hammer-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>
            {selectedStatus === 'active' ? 'Aktif açık artırma bulunmuyor' : 'Açık artırma bulunmuyor'}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  listContainer: {
    padding: 16,
  },
  auctionCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  auctionImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
  },
  auctionInfo: {
    padding: 16,
  },
  auctionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  auctionTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  auctionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  bidInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  bidSection: {
    flex: 1,
  },
  bidLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  currentBid: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  minBid: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF9800',
  },
  nextBidContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  nextBidLabel: {
    fontSize: 12,
    color: '#666',
  },
  nextBidAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  joinButton: {
    backgroundColor: '#FF9800',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  joinButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
});
