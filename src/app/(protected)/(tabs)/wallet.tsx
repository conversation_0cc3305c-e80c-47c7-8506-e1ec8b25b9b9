import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  ScrollView,
  FlatList,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '@clerk/clerk-expo';

interface WalletTopupRequest {
  id: string;
  request_id: string;
  amount: number;
  currency: string;
  payment_method: string;
  status: string;
  created_at: string;
  reason?: string;
}

interface UserWallet {
  id: string;
  name: string;
  wallet_balance: number;
  currency: string;
  loyalty_points: number;
}

export default function WalletScreen() {
  const [topupAmount, setTopupAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('credit_card');
  const [showTopupForm, setShowTopupForm] = useState(false);
  
  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Kullanıcı cüzdan bilgilerini getir
  const { data: userWallet, isLoading: walletLoading } = useQuery({
    queryKey: ['user-wallet', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('users')
        .select('id, name, wallet_balance, currency, loyalty_points')
        .eq('clerk_id', user.id)
        .single();

      if (error) throw error;
      return data as UserWallet;
    },
    enabled: !!user?.id,
  });

  // Cüzdan yükleme taleplerini getir
  const { data: topupRequests, isLoading: requestsLoading } = useQuery({
    queryKey: ['wallet-topup-requests', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];

      const { data, error } = await supabase
        .from('wallet_topup_requests')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data as WalletTopupRequest[];
    },
    enabled: !!user?.id,
  });

  // Cüzdan yükleme talebi oluşturma
  const createTopupRequestMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error('Giriş yapmanız gerekiyor');
      if (!topupAmount || parseFloat(topupAmount) <= 0) {
        throw new Error('Geçerli bir tutar giriniz');
      }

      const amount = parseFloat(topupAmount);
      if (amount < 10 || amount > 10000) {
        throw new Error('Tutar 10 TL ile 10.000 TL arasında olmalıdır');
      }

      const requestId = `WTR-${Date.now()}`;
      
      const { data, error } = await supabase
        .from('wallet_topup_requests')
        .insert({
          id: `${Date.now()}`,
          request_id: requestId,
          user_id: user.id,
          amount: amount,
          currency: 'TRY',
          payment_method: selectedPaymentMethod,
          status: 'pending',
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Cüzdan yükleme talebiniz oluşturuldu. Onay bekliyor.');
      setTopupAmount('');
      setShowTopupForm(false);
      queryClient.invalidateQueries({ queryKey: ['wallet-topup-requests'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Talep oluşturulurken hata oluştu');
    },
  });

  const formatPrice = (price: number, currency: string = 'TRY') => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'processing': return '#2196F3';
      case 'rejected': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'Onaylandı';
      case 'pending': return 'Bekliyor';
      case 'processing': return 'İşleniyor';
      case 'rejected': return 'Reddedildi';
      default: return status;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'credit_card': return 'Kredi Kartı';
      case 'bank_transfer': return 'Banka Havalesi';
      case 'mobile_payment': return 'Mobil Ödeme';
      default: return method;
    }
  };

  const renderTopupRequest = ({ item }: { item: WalletTopupRequest }) => (
    <View style={styles.requestItem}>
      <View style={styles.requestHeader}>
        <Text style={styles.requestId}>{item.request_id}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      
      <View style={styles.requestDetails}>
        <Text style={styles.requestAmount}>
          {formatPrice(item.amount, item.currency)}
        </Text>
        <Text style={styles.requestMethod}>
          {getPaymentMethodText(item.payment_method)}
        </Text>
      </View>
      
      <Text style={styles.requestDate}>
        {new Date(item.created_at).toLocaleDateString('tr-TR')} {new Date(item.created_at).toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
      </Text>
      
      {item.reason && (
        <Text style={styles.requestReason}>Neden: {item.reason}</Text>
      )}
    </View>
  );

  if (!user) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.errorText}>Giriş yapmanız gerekiyor</Text>
      </View>
    );
  }

  if (walletLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Cüzdan bilgileri yükleniyor...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Cüzdan Kartı */}
      <View style={styles.walletCard}>
        <View style={styles.walletHeader}>
          <Text style={styles.walletTitle}>Cüzdanım</Text>
          <Ionicons name="wallet-outline" size={24} color="white" />
        </View>
        
        <Text style={styles.walletBalance}>
          {formatPrice(userWallet?.wallet_balance || 0, userWallet?.currency || 'TRY')}
        </Text>
        
        <View style={styles.walletFooter}>
          <View style={styles.loyaltyPoints}>
            <Ionicons name="star-outline" size={16} color="rgba(255,255,255,0.8)" />
            <Text style={styles.loyaltyText}>
              {userWallet?.loyalty_points || 0} Puan
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.topupButton}
            onPress={() => setShowTopupForm(!showTopupForm)}
          >
            <Ionicons name="add" size={16} color="#007AFF" />
            <Text style={styles.topupButtonText}>Yükle</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Yükleme Formu */}
      {showTopupForm && (
        <View style={styles.topupForm}>
          <Text style={styles.formTitle}>Cüzdan Yükleme</Text>
          
          <Text style={styles.formLabel}>Tutar (TL)</Text>
          <TextInput
            style={styles.amountInput}
            placeholder="Örn: 100"
            value={topupAmount}
            onChangeText={setTopupAmount}
            keyboardType="numeric"
          />
          
          <Text style={styles.formLabel}>Ödeme Yöntemi</Text>
          <View style={styles.paymentMethods}>
            {[
              { key: 'credit_card', label: 'Kredi Kartı', icon: 'card-outline' },
              { key: 'bank_transfer', label: 'Banka Havalesi', icon: 'business-outline' },
              { key: 'mobile_payment', label: 'Mobil Ödeme', icon: 'phone-portrait-outline' },
            ].map((method) => (
              <TouchableOpacity
                key={method.key}
                style={[
                  styles.paymentMethod,
                  selectedPaymentMethod === method.key && styles.paymentMethodSelected
                ]}
                onPress={() => setSelectedPaymentMethod(method.key)}
              >
                <Ionicons 
                  name={method.icon as any} 
                  size={20} 
                  color={selectedPaymentMethod === method.key ? '#007AFF' : '#666'} 
                />
                <Text style={[
                  styles.paymentMethodText,
                  selectedPaymentMethod === method.key && styles.paymentMethodTextSelected
                ]}>
                  {method.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.formButtons}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setShowTopupForm(false);
                setTopupAmount('');
              }}
            >
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.submitButton}
              onPress={() => createTopupRequestMutation.mutate()}
              disabled={createTopupRequestMutation.isPending}
            >
              {createTopupRequestMutation.isPending ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.submitButtonText}>Talep Oluştur</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Yükleme Geçmişi */}
      <View style={styles.historySection}>
        <Text style={styles.historyTitle}>Yükleme Geçmişi</Text>
        
        {requestsLoading ? (
          <ActivityIndicator size="small" color="#007AFF" style={styles.historyLoader} />
        ) : topupRequests && topupRequests.length > 0 ? (
          <FlatList
            data={topupRequests}
            renderItem={renderTopupRequest}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <Text style={styles.noHistoryText}>Henüz yükleme talebi bulunmuyor</Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
  },
  walletCard: {
    backgroundColor: '#007AFF',
    margin: 20,
    padding: 24,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  walletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  walletTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  walletBalance: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16,
  },
  walletFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  loyaltyPoints: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loyaltyText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginLeft: 4,
  },
  topupButton: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  topupButtonText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  topupForm: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  amountInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  paymentMethods: {
    marginBottom: 20,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    marginBottom: 8,
  },
  paymentMethodSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  paymentMethodText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  paymentMethodTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  historySection: {
    backgroundColor: 'white',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  historyLoader: {
    marginVertical: 20,
  },
  noHistoryText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 14,
    marginVertical: 20,
  },
  requestItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingVertical: 12,
    marginBottom: 12,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  requestId: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  requestDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  requestAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  requestMethod: {
    fontSize: 12,
    color: '#666',
  },
  requestDate: {
    fontSize: 12,
    color: '#666',
  },
  requestReason: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
    fontStyle: 'italic',
  },
});
