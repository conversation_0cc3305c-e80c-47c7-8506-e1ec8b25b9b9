import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useSession } from '@clerk/clerk-expo';

const { width } = Dimensions.get('window');

interface Giveaway {
  id: string;
  title_tr: string;
  title_en: string;
  description_tr: string;
  image: string;
  ticket_price: number;
  currency: string;
  total_tickets: number;
  tickets_sold: number;
  status: string;
  start_date: string;
  end_date: string;
  draw_date: string;
  max_tickets_per_user: number;
}

interface GiveawayPrize {
  id: number;
  rank: number;
  title_tr: string;
  description_tr: string;
  value: number;
  currency: string;
  image: string;
  prize_type: string;
}

export default function GiveawaysScreen() {
  const [selectedStatus, setSelectedStatus] = useState<string>('active');
  const supabase = useSupabase();
  const { session } = useSession();
  const queryClient = useQueryClient();

  // Çekilişleri getir
  const { data: giveaways, isLoading, error } = useQuery({
    queryKey: ['giveaways', selectedStatus],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('giveaways')
        .select('*')
        .eq('status', selectedStatus)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Giveaway[];
    },
  });

  // Çekilişe katılma mutation
  const joinGiveawayMutation = useMutation({
    mutationFn: async (giveawayId: string) => {
      if (!session?.user?.id) throw new Error('Giriş yapmanız gerekiyor');

      const { data, error } = await supabase
        .from('giveaway_participants')
        .insert({
          giveaway_id: giveawayId,
          user_id: session.user.id,
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Çekilişe katıldınız!');
      queryClient.invalidateQueries({ queryKey: ['giveaways'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Çekilişe katılırken hata oluştu');
    },
  });

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'completed': return '#2196F3';
      case 'cancelled': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'pending': return 'Bekliyor';
      case 'completed': return 'Tamamlandı';
      case 'cancelled': return 'İptal';
      default: return status;
    }
  };

  const calculateProgress = (ticketsSold: number, totalTickets: number) => {
    return (ticketsSold / totalTickets) * 100;
  };

  const renderGiveaway = ({ item }: { item: Giveaway }) => {
    const progress = calculateProgress(item.tickets_sold, item.total_tickets);
    const isActive = item.status === 'active';
    const endDate = new Date(item.end_date);
    const isExpired = endDate < new Date();

    return (
      <TouchableOpacity
        style={styles.giveawayCard}
        onPress={() => router.push(`/giveaway/${item.id}`)}
      >
        <Image source={{ uri: item.image }} style={styles.giveawayImage} />
        
        <View style={styles.giveawayInfo}>
          <View style={styles.giveawayHeader}>
            <Text style={styles.giveawayTitle} numberOfLines={2}>
              {item.title_tr}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
            </View>
          </View>

          <Text style={styles.giveawayDescription} numberOfLines={2}>
            {item.description_tr}
          </Text>

          {/* Bilet Bilgileri */}
          <View style={styles.ticketInfo}>
            <View style={styles.ticketPrice}>
              <Ionicons name="ticket-outline" size={16} color="#007AFF" />
              <Text style={styles.ticketPriceText}>
                {formatPrice(item.ticket_price, item.currency)}
              </Text>
            </View>
            
            <Text style={styles.ticketCount}>
              {item.tickets_sold}/{item.total_tickets} bilet
            </Text>
          </View>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[styles.progressFill, { width: `${progress}%` }]} 
              />
            </View>
            <Text style={styles.progressText}>{progress.toFixed(1)}%</Text>
          </View>

          {/* Tarih Bilgisi */}
          <View style={styles.dateInfo}>
            <Ionicons name="time-outline" size={14} color="#666" />
            <Text style={styles.dateText}>
              Bitiş: {endDate.toLocaleDateString('tr-TR')}
            </Text>
          </View>

          {/* Katılım Butonu */}
          {isActive && !isExpired && (
            <TouchableOpacity
              style={styles.joinButton}
              onPress={(e) => {
                e.stopPropagation();
                if (!session?.user?.id) {
                  Alert.alert('Giriş Gerekli', 'Çekilişe katılmak için giriş yapmanız gerekiyor');
                  return;
                }
                joinGiveawayMutation.mutate(item.id);
              }}
              disabled={joinGiveawayMutation.isPending}
            >
              {joinGiveawayMutation.isPending ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <Ionicons name="gift-outline" size={16} color="white" />
                  <Text style={styles.joinButtonText}>Katıl</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const statusOptions = [
    { key: 'active', label: 'Aktif' },
    { key: 'pending', label: 'Bekliyor' },
    { key: 'completed', label: 'Tamamlandı' },
  ];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Çekilişler yükleniyor...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Çekilişler yüklenirken hata oluştu</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Status Filter */}
      <View style={styles.filterContainer}>
        {statusOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.filterButton,
              selectedStatus === option.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedStatus(option.key)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedStatus === option.key && styles.filterButtonTextActive
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Giveaways List */}
      {giveaways && giveaways.length > 0 ? (
        <FlatList
          data={giveaways}
          renderItem={renderGiveaway}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="gift-outline" size={64} color="#ccc" />
          <Text style={styles.emptyText}>
            {selectedStatus === 'active' ? 'Aktif çekiliş bulunmuyor' : 'Çekiliş bulunmuyor'}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF6B6B',
    textAlign: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  listContainer: {
    padding: 16,
  },
  giveawayCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  giveawayImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
  },
  giveawayInfo: {
    padding: 16,
  },
  giveawayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  giveawayTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  giveawayDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  ticketInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  ticketPrice: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ticketPriceText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginLeft: 4,
  },
  ticketCount: {
    fontSize: 14,
    color: '#666',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e0e0e0',
    borderRadius: 3,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  joinButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  joinButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
});
