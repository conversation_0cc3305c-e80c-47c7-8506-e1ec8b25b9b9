import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';

interface Product {
  id: string;
  name_tr: string;
  name_en: string;
  description_tr: string;
  description_en: string;
  image: string;
  price: number;
  currency: string;
  stock: number;
  is_available: boolean;
  categories?: {
    id: string;
    name_tr: string;
    name_en: string;
  }[];
}

interface Category {
  id: string;
  name_tr: string;
  name_en: string;
  image: string;
}

export default function ProductsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const supabase = useSupabase();

  // Kategorileri getir
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name_tr');
      
      if (error) throw error;
      return data as Category[];
    },
  });

  // Ürünleri getir
  const { data: products, isLoading: productsLoading, error } = useQuery({
    queryKey: ['products', searchQuery, selectedCategory],
    queryFn: async () => {
      let query = supabase
        .from('products')
        .select(`
          *,
          product_categories!inner(
            categories(id, name_tr, name_en)
          )
        `)
        .eq('is_available', true)
        .order('name_tr');

      if (searchQuery) {
        query = query.or(`name_tr.ilike.%${searchQuery}%,name_en.ilike.%${searchQuery}%`);
      }

      if (selectedCategory) {
        query = query.eq('product_categories.category_id', selectedCategory);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      // Kategorileri düzenle
      const processedData = data?.map(product => ({
        ...product,
        categories: product.product_categories?.map((pc: any) => pc.categories) || []
      }));
      
      return processedData as Product[];
    },
  });

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const handleAddToCart = (product: Product) => {
    Alert.alert(
      'Sepete Ekle',
      `${product.name_tr} sepete eklensin mi?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Ekle', onPress: () => console.log('Sepete eklendi:', product.id) }
      ]
    );
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.selectedCategory
      ]}
      onPress={() => setSelectedCategory(selectedCategory === item.id ? null : item.id)}
    >
      <Image source={{ uri: item.image }} style={styles.categoryImage} />
      <Text style={[
        styles.categoryText,
        selectedCategory === item.id && styles.selectedCategoryText
      ]}>
        {item.name_tr}
      </Text>
    </TouchableOpacity>
  );

  const renderProduct = ({ item }: { item: Product }) => (
    <View style={styles.productCard}>
      <Image source={{ uri: item.image }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.name_tr}</Text>
        <Text style={styles.productDescription} numberOfLines={2}>
          {item.description_tr}
        </Text>
        <View style={styles.categoryTags}>
          {item.categories?.map((category) => (
            <View key={category.id} style={styles.categoryTag}>
              <Text style={styles.categoryTagText}>{category.name_tr}</Text>
            </View>
          ))}
        </View>
        <View style={styles.productFooter}>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>{formatPrice(item.price, item.currency)}</Text>
            <Text style={styles.stock}>Stok: {item.stock}</Text>
          </View>
          <TouchableOpacity
            style={styles.addToCartButton}
            onPress={() => handleAddToCart(item)}
          >
            <Ionicons name="cart-outline" size={20} color="white" />
            <Text style={styles.addToCartText}>Sepete Ekle</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Ürünler yüklenirken hata oluştu</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Arama Çubuğu */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Ürün ara..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Kategoriler */}
      <View style={styles.categoriesSection}>
        <Text style={styles.sectionTitle}>Kategoriler</Text>
        {categoriesLoading ? (
          <ActivityIndicator size="small" color="#007AFF" />
        ) : (
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        )}
      </View>

      {/* Ürünler */}
      <View style={styles.productsSection}>
        <Text style={styles.sectionTitle}>
          Ürünler {selectedCategory && categories && `- ${categories.find(c => c.id === selectedCategory)?.name_tr}`}
        </Text>
        {productsLoading ? (
          <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
        ) : (
          <FlatList
            data={products}
            renderItem={renderProduct}
            keyExtractor={(item) => item.id}
            numColumns={2}
            columnWrapperStyle={styles.productRow}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.productsList}
          />
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    margin: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  categoriesSection: {
    backgroundColor: 'white',
    paddingVertical: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginBottom: 12,
    color: '#333',
  },
  categoriesList: {
    paddingHorizontal: 16,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: 16,
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
    minWidth: 80,
  },
  selectedCategory: {
    backgroundColor: '#007AFF',
  },
  categoryImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  categoryText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#333',
  },
  selectedCategoryText: {
    color: 'white',
  },
  productsSection: {
    flex: 1,
    backgroundColor: 'white',
    paddingTop: 16,
  },
  loader: {
    marginTop: 50,
  },
  productsList: {
    paddingHorizontal: 16,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  productCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: '48%',
  },
  productImage: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
    lineHeight: 16,
  },
  categoryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryTag: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 2,
  },
  categoryTagText: {
    fontSize: 10,
    color: '#1976d2',
  },
  productFooter: {
    marginTop: 8,
  },
  priceContainer: {
    marginBottom: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  stock: {
    fontSize: 12,
    color: '#666',
  },
  addToCartButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 6,
  },
  addToCartText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
  },
});
