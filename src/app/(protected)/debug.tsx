import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../lib/supabase';
import { useUser } from '@clerk/clerk-expo';
import { Stack } from 'expo-router';

export default function DebugScreen() {
  const [testResults, setTestResults] = useState<any>({});
  const supabase = useSupabase();
  const { user } = useUser();

  // Test database connection
  const { data: dbTest, isLoading: dbLoading } = useQuery({
    queryKey: ['db-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('categories')
          .select('count')
          .limit(1);
        
        return { success: true, error: null, data };
      } catch (error) {
        return { success: false, error: error.message, data: null };
      }
    },
  });

  // Test user data
  const { data: userTest, isLoading: userLoading } = useQuery({
    queryKey: ['user-test', user?.id],
    queryFn: async () => {
      if (!user?.id) return { success: false, error: 'No user ID' };
      
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('clerk_id', user.id)
          .single();
        
        return { success: true, error: null, data };
      } catch (error) {
        return { success: false, error: error.message, data: null };
      }
    },
    enabled: !!user?.id,
  });

  // Test categories
  const { data: categoriesTest, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .limit(5);
        
        return { success: true, error: null, data, count: data?.length || 0 };
      } catch (error) {
        return { success: false, error: error.message, data: null, count: 0 };
      }
    },
  });

  // Test products
  const { data: productsTest, isLoading: productsLoading } = useQuery({
    queryKey: ['products-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .limit(5);
        
        return { success: true, error: null, data, count: data?.length || 0 };
      } catch (error) {
        return { success: false, error: error.message, data: null, count: 0 };
      }
    },
  });

  const runManualTests = async () => {
    const results: any = {};

    // Test 1: Basic connection
    try {
      const { data, error } = await supabase.from('categories').select('count');
      results.basicConnection = { success: !error, error: error?.message };
    } catch (error) {
      results.basicConnection = { success: false, error: error.message };
    }

    // Test 2: Insert test
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          name_tr: 'Test Kategori',
          image: 'https://via.placeholder.com/150',
          is_active: true,
        })
        .select()
        .single();
      
      results.insertTest = { success: !error, error: error?.message, data };
      
      // Clean up test data
      if (data?.id) {
        await supabase.from('categories').delete().eq('id', data.id);
      }
    } catch (error) {
      results.insertTest = { success: false, error: error.message };
    }

    setTestResults(results);
  };

  const isLoading = dbLoading || userLoading || categoriesLoading || productsLoading;

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Debug Bilgileri',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <ScrollView style={styles.container}>
        <Text style={styles.title}>🔍 Debug Bilgileri</Text>

        {/* Clerk User Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Clerk Kullanıcı Bilgileri</Text>
          <Text style={styles.info}>ID: {user?.id || 'Yok'}</Text>
          <Text style={styles.info}>Email: {user?.primaryEmailAddress?.emailAddress || 'Yok'}</Text>
          <Text style={styles.info}>İsim: {user?.fullName || 'Yok'}</Text>
        </View>

        {/* Database Connection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🗄️ Database Bağlantısı</Text>
          {dbLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, dbTest?.success ? styles.success : styles.error]}>
                {dbTest?.success ? '✅ Bağlantı OK' : '❌ Bağlantı Hatası'}
              </Text>
              {dbTest?.error && <Text style={styles.errorText}>{dbTest.error}</Text>}
            </View>
          )}
        </View>

        {/* User in Database */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👥 Database'de Kullanıcı</Text>
          {userLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, userTest?.success ? styles.success : styles.error]}>
                {userTest?.success ? '✅ Kullanıcı Bulundu' : '❌ Kullanıcı Bulunamadı'}
              </Text>
              {userTest?.data && (
                <View>
                  <Text style={styles.info}>Rol: {userTest.data.role || 'Yok'}</Text>
                  <Text style={styles.info}>İzinler: {userTest.data.role_permissions?.join(', ') || 'Yok'}</Text>
                  <Text style={styles.info}>Bakiye: ₺{userTest.data.wallet_balance || 0}</Text>
                </View>
              )}
              {userTest?.error && <Text style={styles.errorText}>{userTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Categories Test */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📂 Kategoriler</Text>
          {categoriesLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, categoriesTest?.success ? styles.success : styles.error]}>
                {categoriesTest?.success ? `✅ ${categoriesTest.count} kategori bulundu` : '❌ Kategori hatası'}
              </Text>
              {categoriesTest?.error && <Text style={styles.errorText}>{categoriesTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Products Test */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📦 Ürünler</Text>
          {productsLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, productsTest?.success ? styles.success : styles.error]}>
                {productsTest?.success ? `✅ ${productsTest.count} ürün bulundu` : '❌ Ürün hatası'}
              </Text>
              {productsTest?.error && <Text style={styles.errorText}>{productsTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Manual Tests */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🧪 Manuel Testler</Text>
          <TouchableOpacity style={styles.testButton} onPress={runManualTests}>
            <Text style={styles.testButtonText}>Testleri Çalıştır</Text>
          </TouchableOpacity>
          
          {Object.keys(testResults).length > 0 && (
            <View style={styles.testResults}>
              {Object.entries(testResults).map(([key, result]: [string, any]) => (
                <View key={key} style={styles.testResult}>
                  <Text style={styles.testName}>{key}:</Text>
                  <Text style={[styles.status, result.success ? styles.success : styles.error]}>
                    {result.success ? '✅ Başarılı' : '❌ Başarısız'}
                  </Text>
                  {result.error && <Text style={styles.errorText}>{result.error}</Text>}
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Environment Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🌍 Ortam Bilgileri</Text>
          <Text style={styles.info}>Supabase URL: {process.env.EXPO_PUBLIC_SUPABASE_URL?.slice(0, 30)}...</Text>
          <Text style={styles.info}>Supabase Key: {process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY?.slice(0, 30)}...</Text>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  info: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  status: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  success: {
    color: '#34C759',
  },
  error: {
    color: '#FF3B30',
  },
  errorText: {
    fontSize: 12,
    color: '#FF3B30',
    fontStyle: 'italic',
    marginTop: 4,
  },
  testButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  testResults: {
    marginTop: 12,
  },
  testResult: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  testName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
});
