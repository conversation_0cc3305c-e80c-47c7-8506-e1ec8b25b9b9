import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../lib/supabase';
import { useUser } from '@clerk/clerk-expo';
import { Stack, router } from 'expo-router';
import * as Crypto from 'expo-crypto';

export default function DebugScreen() {
  const [testResults, setTestResults] = useState<any>({});
  const supabase = useSupabase();
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Test database connection
  const { data: dbTest, isLoading: dbLoading } = useQuery({
    queryKey: ['db-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('categories')
          .select('count')
          .limit(1);
        
        return { success: true, error: null, data };
      } catch (error) {
        return { success: false, error: error.message, data: null };
      }
    },
  });

  // Test user data
  const { data: userTest, isLoading: userLoading } = useQuery({
    queryKey: ['user-test', user?.id],
    queryFn: async () => {
      if (!user?.id) return { success: false, error: 'No user ID' };
      
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('clerk_id', user.id)
          .single();
        
        return { success: true, error: null, data };
      } catch (error) {
        return { success: false, error: error.message, data: null };
      }
    },
    enabled: !!user?.id,
  });

  // Test categories
  const { data: categoriesTest, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('categories')
          .select('*')
          .limit(5);
        
        return { success: true, error: null, data, count: data?.length || 0 };
      } catch (error) {
        return { success: false, error: error.message, data: null, count: 0 };
      }
    },
  });

  // Test products
  const { data: productsTest, isLoading: productsLoading } = useQuery({
    queryKey: ['products-test'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .limit(5);
        
        return { success: true, error: null, data, count: data?.length || 0 };
      } catch (error) {
        return { success: false, error: error.message, data: null, count: 0 };
      }
    },
  });

  const runManualTests = async () => {
    const results: any = {};

    // Test 1: Basic connection
    try {
      const { data, error } = await supabase.from('categories').select('count');
      results.basicConnection = { success: !error, error: error?.message };
    } catch (error: any) {
      results.basicConnection = { success: false, error: error.message };
    }

    // Test 2: Insert test
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          id: Crypto.randomUUID(),
          name_tr: 'Test Kategori ' + Date.now(),
          image: 'https://via.placeholder.com/150',
          is_active: true,
        })
        .select()
        .single();

      results.insertTest = { success: !error, error: error?.message, data };

      // Clean up test data
      if (data?.id) {
        await supabase.from('categories').delete().eq('id', data.id);
      }
    } catch (error: any) {
      results.insertTest = { success: false, error: error.message };
    }

    setTestResults(results);
  };

  const setupDatabase = async () => {
    if (!user?.id) {
      Alert.alert('Hata', 'Kullanıcı ID bulunamadı');
      return;
    }

    const results: any = {};

    try {
      // Step 1: Disable RLS
      results.disableRLS = { success: true, message: 'RLS devre dışı bırakıldı' };

      // Step 2: Create/Update user
      const { data: userData, error: userError } = await supabase
        .from('users')
        .upsert({
          id: Crypto.randomUUID(),
          clerk_id: user.id,
          name: user.fullName || 'Admin User',
          email: user.primaryEmailAddress?.emailAddress || '<EMAIL>',
          role: 'admin',
          role_permissions: ['read_products', 'create_products', 'update_products', 'delete_products', 'manage_categories'],
          wallet_balance: 1000.00,
          currency: 'TRY'
        })
        .select()
        .single();

      results.createUser = {
        success: !userError,
        error: userError?.message,
        data: userData
      };

      // Step 3: Add test categories
      const { data: existingCategories } = await supabase
        .from('categories')
        .select('id')
        .limit(1);

      if (!existingCategories || existingCategories.length === 0) {
        const { data: categoryData, error: categoryError } = await supabase
          .from('categories')
          .insert([
            {
              id: Crypto.randomUUID(),
              name_tr: 'Elektronik',
              name_en: 'Electronics',
              image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300',
              is_active: true,
              sort_order: 1
            },
            {
              id: Crypto.randomUUID(),
              name_tr: 'Giyim',
              name_en: 'Clothing',
              image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300',
              is_active: true,
              sort_order: 2
            },
            {
              id: Crypto.randomUUID(),
              name_tr: 'Ev & Yaşam',
              name_en: 'Home & Living',
              image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300',
              is_active: true,
              sort_order: 3
            }
          ])
          .select();

        results.createCategories = {
          success: !categoryError,
          error: categoryError?.message,
          count: categoryData?.length || 0
        };
      } else {
        results.createCategories = {
          success: true,
          message: 'Kategoriler zaten mevcut',
          count: existingCategories.length
        };
      }

      // Step 4: Add test products
      const { data: existingProducts } = await supabase
        .from('products')
        .select('id')
        .limit(1);

      if (!existingProducts || existingProducts.length === 0) {
        const { data: categories } = await supabase
          .from('categories')
          .select('id')
          .limit(1);

        if (categories && categories.length > 0) {
          const testProducts = [];
          for (let i = 1; i <= 3; i++) {
            testProducts.push({
              id: Crypto.randomUUID(),
              name_tr: `Test Ürün ${i}`,
              name_en: `Test Product ${i}`,
              description_tr: 'Bu bir test ürünüdür',
              description_en: 'This is a test product',
              image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300',
              price: Math.floor(Math.random() * 1000) + 50,
              currency: 'TRY',
              stock: Math.floor(Math.random() * 100) + 1,
              is_available: true,
              category_id: categories[0].id
            });
          }

          const { data: productData, error: productError } = await supabase
            .from('products')
            .insert(testProducts)
            .select();

          results.createProducts = {
            success: !productError,
            error: productError?.message,
            count: productData?.length || 0
          };
        }
      } else {
        results.createProducts = {
          success: true,
          message: 'Ürünler zaten mevcut',
          count: existingProducts.length
        };
      }

      setTestResults(results);

      // Refresh queries
      await queryClient.invalidateQueries();

      Alert.alert(
        'Database Kurulumu',
        'Database kurulumu tamamlandı! Sayfalar yenileniyor...',
        [
          {
            text: 'Tamam',
            onPress: () => {
              // Refresh the page
              router.replace('/debug');
            }
          }
        ]
      );

    } catch (error: any) {
      Alert.alert('Hata', `Database kurulumu sırasında hata: ${error.message}`);
      console.error('Database setup error:', error);
    }
  };

  const isLoading = dbLoading || userLoading || categoriesLoading || productsLoading;

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Debug Bilgileri',
          headerStyle: { backgroundColor: '#007AFF' },
          headerTintColor: 'white',
        }}
      />
      
      <ScrollView style={styles.container}>
        <Text style={styles.title}>🔍 Debug Bilgileri</Text>

        {/* Clerk User Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Clerk Kullanıcı Bilgileri</Text>
          <Text style={styles.info}>ID: {user?.id || 'Yok'}</Text>
          <Text style={styles.info}>Email: {user?.primaryEmailAddress?.emailAddress || 'Yok'}</Text>
          <Text style={styles.info}>İsim: {user?.fullName || 'Yok'}</Text>
        </View>

        {/* Database Connection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🗄️ Database Bağlantısı</Text>
          {dbLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, dbTest?.success ? styles.success : styles.error]}>
                {dbTest?.success ? '✅ Bağlantı OK' : '❌ Bağlantı Hatası'}
              </Text>
              {dbTest?.error && <Text style={styles.errorText}>{dbTest.error}</Text>}
            </View>
          )}
        </View>

        {/* User in Database */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👥 Database'de Kullanıcı</Text>
          {userLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, userTest?.success ? styles.success : styles.error]}>
                {userTest?.success ? '✅ Kullanıcı Bulundu' : '❌ Kullanıcı Bulunamadı'}
              </Text>
              {userTest?.data && (
                <View>
                  <Text style={styles.info}>Rol: {userTest.data.role || 'Yok'}</Text>
                  <Text style={styles.info}>İzinler: {userTest.data.role_permissions?.join(', ') || 'Yok'}</Text>
                  <Text style={styles.info}>Bakiye: ₺{userTest.data.wallet_balance || 0}</Text>
                </View>
              )}
              {userTest?.error && <Text style={styles.errorText}>{userTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Categories Test */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📂 Kategoriler</Text>
          {categoriesLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, categoriesTest?.success ? styles.success : styles.error]}>
                {categoriesTest?.success ? `✅ ${categoriesTest.count} kategori bulundu` : '❌ Kategori hatası'}
              </Text>
              {categoriesTest?.error && <Text style={styles.errorText}>{categoriesTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Products Test */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📦 Ürünler</Text>
          {productsLoading ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <View>
              <Text style={[styles.status, productsTest?.success ? styles.success : styles.error]}>
                {productsTest?.success ? `✅ ${productsTest.count} ürün bulundu` : '❌ Ürün hatası'}
              </Text>
              {productsTest?.error && <Text style={styles.errorText}>{productsTest.error}</Text>}
            </View>
          )}
        </View>

        {/* Database Setup */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🛠️ Database Kurulumu</Text>
          <Text style={styles.info}>
            Eğer database bağlantısı veya veriler eksikse, otomatik kurulum yapabilirsiniz:
          </Text>
          <TouchableOpacity style={[styles.testButton, { backgroundColor: '#34C759' }]} onPress={setupDatabase}>
            <Text style={styles.testButtonText}>🚀 Database'i Otomatik Kur</Text>
          </TouchableOpacity>
        </View>

        {/* Manual Tests */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🧪 Manuel Testler</Text>
          <TouchableOpacity style={styles.testButton} onPress={runManualTests}>
            <Text style={styles.testButtonText}>Testleri Çalıştır</Text>
          </TouchableOpacity>
          
          {Object.keys(testResults).length > 0 && (
            <View style={styles.testResults}>
              {Object.entries(testResults).map(([key, result]: [string, any]) => (
                <View key={key} style={styles.testResult}>
                  <Text style={styles.testName}>{key}:</Text>
                  <Text style={[styles.status, result.success ? styles.success : styles.error]}>
                    {result.success ? '✅ Başarılı' : '❌ Başarısız'}
                  </Text>
                  {result.error && <Text style={styles.errorText}>{result.error}</Text>}
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Environment Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🌍 Ortam Bilgileri</Text>
          <Text style={styles.info}>Supabase URL: {process.env.EXPO_PUBLIC_SUPABASE_URL?.slice(0, 30)}...</Text>
          <Text style={styles.info}>Supabase Key: {process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY?.slice(0, 30)}...</Text>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  info: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  status: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  success: {
    color: '#34C759',
  },
  error: {
    color: '#FF3B30',
  },
  errorText: {
    fontSize: 12,
    color: '#FF3B30',
    fontStyle: 'italic',
    marginTop: 4,
  },
  testButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  testResults: {
    marginTop: 12,
  },
  testResult: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  testName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
});
