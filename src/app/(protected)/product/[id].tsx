import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  TextInput,
  FlatList,
} from 'react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { useSession } from '@clerk/clerk-expo';

const { width } = Dimensions.get('window');

interface Product {
  id: string;
  name_tr: string;
  name_en: string;
  description_tr: string;
  description_en: string;
  image: string;
  price: number;
  currency: string;
  stock: number;
  is_available: boolean;
  categories?: {
    id: string;
    name_tr: string;
    name_en: string;
  }[];
}

interface Review {
  id: number;
  rating: number;
  review_text: string;
  created_at: string;
  users: {
    id: string;
    name: string;
    image: string;
  };
}

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [quantity, setQuantity] = useState(1);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [newReview, setNewReview] = useState('');
  const [newRating, setNewRating] = useState(5);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const supabase = useSupabase();
  const { session } = useSession();
  const queryClient = useQueryClient();

  // Ürün detayını getir
  const { data: product, isLoading, error } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          product_categories(
            categories(id, name_tr, name_en)
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      // Kategorileri düzenle
      const processedData = {
        ...data,
        categories: data.product_categories?.map((pc: any) => pc.categories) || []
      };

      return processedData as Product;
    },
  });

  // Wishlist durumunu kontrol et
  const { data: wishlistStatus } = useQuery({
    queryKey: ['wishlist', id, session?.user?.id],
    queryFn: async () => {
      if (!session?.user?.id) return false;

      const { data, error } = await supabase
        .from('wishlists')
        .select('id')
        .eq('user_id', session.user.id)
        .eq('product_id', id)
        .single();

      return !!data;
    },
    enabled: !!session?.user?.id,
  });

  // Yorumları getir
  const { data: reviews, isLoading: reviewsLoading } = useQuery({
    queryKey: ['reviews', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('product_reviews')
        .select(`
          id,
          rating,
          review_text,
          created_at,
          users(id, name, image)
        `)
        .eq('product_id', id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Review[];
    },
  });

  // Sepete ekleme mutation
  const addToCartMutation = useMutation({
    mutationFn: async () => {
      if (!session?.user?.id) throw new Error('Giriş yapmanız gerekiyor');
      
      const { data, error } = await supabase
        .from('cart_items')
        .upsert({
          user_id: session.user.id,
          product_id: id,
          quantity: quantity,
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Ürün sepete eklendi!');
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Sepete eklenirken hata oluştu');
    },
  });

  // Wishlist toggle mutation
  const toggleWishlistMutation = useMutation({
    mutationFn: async () => {
      if (!session?.user?.id) throw new Error('Giriş yapmanız gerekiyor');

      if (wishlistStatus) {
        // Wishlist'ten kaldır
        const { error } = await supabase
          .from('wishlists')
          .delete()
          .eq('user_id', session.user.id)
          .eq('product_id', id);

        if (error) throw error;
        return false;
      } else {
        // Wishlist'e ekle
        const { error } = await supabase
          .from('wishlists')
          .insert({
            user_id: session.user.id,
            product_id: id,
          });

        if (error) throw error;
        return true;
      }
    },
    onSuccess: (isAdded) => {
      setIsInWishlist(isAdded);
      Alert.alert(
        'Başarılı',
        isAdded ? 'Favorilere eklendi!' : 'Favorilerden kaldırıldı!'
      );
      queryClient.invalidateQueries({ queryKey: ['wishlist', id, session?.user?.id] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'İşlem sırasında hata oluştu');
    },
  });

  // Yorum ekleme mutation
  const addReviewMutation = useMutation({
    mutationFn: async () => {
      if (!session?.user?.id) throw new Error('Giriş yapmanız gerekiyor');
      if (!newReview.trim()) throw new Error('Yorum boş olamaz');

      const { data, error } = await supabase
        .from('product_reviews')
        .insert({
          product_id: id,
          user_id: session.user.id,
          rating: newRating,
          review_text: newReview.trim(),
        })
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      Alert.alert('Başarılı', 'Yorumunuz eklendi!');
      setNewReview('');
      setNewRating(5);
      setShowReviewForm(false);
      queryClient.invalidateQueries({ queryKey: ['reviews', id] });
    },
    onError: (error: any) => {
      Alert.alert('Hata', error.message || 'Yorum eklenirken hata oluştu');
    },
  });

  const formatPrice = (price: number, currency: string) => {
    if (currency === 'USD') {
      return `$${price.toFixed(2)}`;
    } else if (currency === 'TRY') {
      return `₺${price.toFixed(2)}`;
    }
    return `${price.toFixed(2)} ${currency}`;
  };

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= (product?.stock || 0)) {
      setQuantity(newQuantity);
    }
  };

  const renderStars = (rating: number, size: number = 16) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= rating ? "star" : "star-outline"}
            size={size}
            color={star <= rating ? "#FFD700" : "#ccc"}
          />
        ))}
      </View>
    );
  };

  const renderReview = ({ item }: { item: Review }) => (
    <View style={styles.reviewItem}>
      <View style={styles.reviewHeader}>
        <Image
          source={{ uri: item.users?.image || 'https://via.placeholder.com/40' }}
          style={styles.reviewerAvatar}
        />
        <View style={styles.reviewerInfo}>
          <Text style={styles.reviewerName}>{item.users?.name || 'Anonim'}</Text>
          <View style={styles.reviewMeta}>
            {renderStars(item.rating)}
            <Text style={styles.reviewDate}>
              {new Date(item.created_at).toLocaleDateString('tr-TR')}
            </Text>
          </View>
        </View>
      </View>
      {item.review_text && (
        <Text style={styles.reviewText}>{item.review_text}</Text>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Ürün yükleniyor...</Text>
      </View>
    );
  }

  if (error || !product) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Ürün bulunamadı</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Geri Dön</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: product.name_tr,
          headerRight: () => (
            <TouchableOpacity
              onPress={() => toggleWishlistMutation.mutate()}
              disabled={toggleWishlistMutation.isPending}
            >
              <Ionicons
                name={wishlistStatus ? "heart" : "heart-outline"}
                size={24}
                color={wishlistStatus ? "#FF6B6B" : "#333"}
              />
            </TouchableOpacity>
          ),
        }}
      />
      
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Ürün Resmi */}
        <Image source={{ uri: product.image }} style={styles.productImage} />

        {/* Ürün Bilgileri */}
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name_tr}</Text>
          
          {/* Kategoriler */}
          <View style={styles.categoriesContainer}>
            {product.categories?.map((category) => (
              <View key={category.id} style={styles.categoryTag}>
                <Text style={styles.categoryTagText}>{category.name_tr}</Text>
              </View>
            ))}
          </View>

          {/* Fiyat ve Stok */}
          <View style={styles.priceStockContainer}>
            <Text style={styles.price}>{formatPrice(product.price, product.currency)}</Text>
            <View style={styles.stockContainer}>
              <Ionicons name="cube-outline" size={16} color="#666" />
              <Text style={styles.stockText}>Stok: {product.stock}</Text>
            </View>
          </View>

          {/* Açıklama */}
          <Text style={styles.sectionTitle}>Ürün Açıklaması</Text>
          <Text style={styles.description}>{product.description_tr}</Text>

          {/* Miktar Seçici */}
          <Text style={styles.sectionTitle}>Miktar</Text>
          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
            >
              <Ionicons name="remove" size={20} color={quantity <= 1 ? "#ccc" : "#007AFF"} />
            </TouchableOpacity>

            <Text style={styles.quantityText}>{quantity}</Text>

            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(1)}
              disabled={quantity >= product.stock}
            >
              <Ionicons name="add" size={20} color={quantity >= product.stock ? "#ccc" : "#007AFF"} />
            </TouchableOpacity>
          </View>

          {/* Yorumlar Bölümü */}
          <View style={styles.reviewsSection}>
            <View style={styles.reviewsHeader}>
              <Text style={styles.sectionTitle}>
                Yorumlar ({reviews?.length || 0})
              </Text>
              {session?.user?.id && (
                <TouchableOpacity
                  style={styles.addReviewButton}
                  onPress={() => setShowReviewForm(!showReviewForm)}
                >
                  <Ionicons name="add" size={20} color="#007AFF" />
                  <Text style={styles.addReviewText}>Yorum Yap</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Yorum Ekleme Formu */}
            {showReviewForm && (
              <View style={styles.reviewForm}>
                <Text style={styles.formLabel}>Puanınız:</Text>
                <View style={styles.ratingSelector}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <TouchableOpacity
                      key={star}
                      onPress={() => setNewRating(star)}
                    >
                      <Ionicons
                        name={star <= newRating ? "star" : "star-outline"}
                        size={24}
                        color={star <= newRating ? "#FFD700" : "#ccc"}
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                <Text style={styles.formLabel}>Yorumunuz:</Text>
                <TextInput
                  style={styles.reviewInput}
                  multiline
                  numberOfLines={4}
                  placeholder="Ürün hakkındaki düşüncelerinizi paylaşın..."
                  value={newReview}
                  onChangeText={setNewReview}
                />

                <View style={styles.formButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowReviewForm(false);
                      setNewReview('');
                      setNewRating(5);
                    }}
                  >
                    <Text style={styles.cancelButtonText}>İptal</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.submitButton}
                    onPress={() => addReviewMutation.mutate()}
                    disabled={addReviewMutation.isPending}
                  >
                    {addReviewMutation.isPending ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <Text style={styles.submitButtonText}>Gönder</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Yorumlar Listesi */}
            {reviewsLoading ? (
              <ActivityIndicator size="small" color="#007AFF" style={styles.reviewsLoader} />
            ) : reviews && reviews.length > 0 ? (
              <FlatList
                data={reviews}
                renderItem={renderReview}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <Text style={styles.noReviewsText}>Henüz yorum yapılmamış</Text>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Alt Butonlar */}
      <View style={styles.bottomContainer}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Toplam:</Text>
          <Text style={styles.totalPrice}>
            {formatPrice(product.price * quantity, product.currency)}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.addToCartButton,
            (!product.is_available || product.stock === 0) && styles.disabledButton
          ]}
          onPress={() => addToCartMutation.mutate()}
          disabled={!product.is_available || product.stock === 0 || addToCartMutation.isPending}
        >
          {addToCartMutation.isPending ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="cart-outline" size={20} color="white" />
              <Text style={styles.addToCartText}>
                {!product.is_available || product.stock === 0 ? 'Stokta Yok' : 'Sepete Ekle'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  productImage: {
    width: width,
    height: width * 0.8,
    backgroundColor: '#f0f0f0',
  },
  productInfo: {
    backgroundColor: 'white',
    padding: 20,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  categoryTag: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
  },
  categoryTagText: {
    fontSize: 12,
    color: '#1976d2',
    fontWeight: '600',
  },
  priceStockContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stockText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 16,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 20,
    minWidth: 30,
    textAlign: 'center',
  },
  bottomContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 16,
    color: '#666',
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addToCartButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  addToCartText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Yorum stilleri
  reviewsSection: {
    marginTop: 20,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addReviewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  addReviewText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  reviewForm: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  ratingSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  reviewInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
    textAlignVertical: 'top',
    marginBottom: 16,
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  reviewsLoader: {
    marginVertical: 20,
  },
  noReviewsText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
    marginVertical: 20,
  },
  reviewItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  reviewHeader: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  reviewMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  reviewDate: {
    fontSize: 12,
    color: '#666',
  },
  reviewText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
});
