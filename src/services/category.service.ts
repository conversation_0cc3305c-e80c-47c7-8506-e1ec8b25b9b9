import { BaseService } from './base.service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

export interface CreateCategoryData {
  name_tr: string;
  name_en?: string;
  description_tr?: string;
  description_en?: string;
  image: string;
  is_active?: boolean;
  parent_id?: string;
  icon?: string;
  sort_order?: number;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string;
}

export interface CategoryFilters {
  search?: string;
  is_active?: boolean;
  parent_id?: string;
  limit?: number;
  offset?: number;
}

export interface Category {
  id: string;
  name_tr: string;
  name_en?: string;
  description_tr?: string;
  description_en?: string;
  image: string;
  is_active: boolean;
  parent_id?: string;
  icon?: string;
  slug?: string;
  sort_order: number;
  created_at: string;
  updated_at?: string;
}

export class CategoryService extends BaseService {
  constructor(supabase: SupabaseClient<Database>) {
    super(supabase);
  }

  /**
   * Get all categories with filters
   */
  async getCategories(filters: CategoryFilters = {}): Promise<Category[]> {
    try {
      let query = this.supabase
        .from('categories')
        .select('*')
        .order('sort_order', { ascending: true })
        .order('name_tr', { ascending: true });

      // Apply filters
      if (filters.search) {
        query = query.or(`name_tr.ilike.%${filters.search}%,name_en.ilike.%${filters.search}%`);
      }

      if (filters.is_active !== undefined) {
        query = query.eq('is_active', filters.is_active);
      }

      if (filters.parent_id !== undefined) {
        if (filters.parent_id === null) {
          query = query.is('parent_id', null);
        } else {
          query = query.eq('parent_id', filters.parent_id);
        }
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        this.handleError(error, 'getCategories');
      }

      return data as Category[];
    } catch (error) {
      this.handleError(error, 'getCategories');
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id: string): Promise<Category> {
    try {
      const { data, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        this.handleError(error, 'getCategoryById');
      }

      if (!data) {
        throw new Error('Kategori bulunamadı');
      }

      return data as Category;
    } catch (error) {
      this.handleError(error, 'getCategoryById');
    }
  }

  /**
   * Create new category
   */
  async createCategory(userId: string, categoryData: CreateCategoryData): Promise<Category> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'manage_categories');

      // Validate required fields
      this.validateRequired(categoryData, ['name_tr', 'image']);

      // Generate slug
      const slug = this.generateSlug(categoryData.name_tr);

      // Check if slug already exists
      const { data: existingCategory } = await this.supabase
        .from('categories')
        .select('id')
        .eq('slug', slug)
        .single();

      if (existingCategory) {
        throw new Error('Bu isimde bir kategori zaten mevcut');
      }

      // Prepare data
      const dataToInsert = {
        ...categoryData,
        name_en: categoryData.name_en || categoryData.name_tr,
        description_en: categoryData.description_en || categoryData.description_tr,
        is_active: categoryData.is_active ?? true,
        sort_order: categoryData.sort_order ?? 0,
        slug,
      };

      // Insert category
      const { data, error } = await this.supabase
        .from('categories')
        .insert(dataToInsert)
        .select()
        .single();

      if (error) {
        this.handleError(error, 'createCategory');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'create_category',
        'category',
        data.id,
        { category_data: dataToInsert }
      );

      return data as Category;
    } catch (error) {
      this.handleError(error, 'createCategory');
    }
  }

  /**
   * Update category
   */
  async updateCategory(userId: string, updateData: UpdateCategoryData): Promise<Category> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'manage_categories');

      // Validate required fields
      this.validateRequired(updateData, ['id']);

      // Get existing category
      const existingCategory = await this.getCategoryById(updateData.id);

      // Generate new slug if name changed
      let slug = existingCategory.slug;
      if (updateData.name_tr && updateData.name_tr !== existingCategory.name_tr) {
        slug = this.generateSlug(updateData.name_tr);

        // Check if new slug already exists
        const { data: existingSlug } = await this.supabase
          .from('categories')
          .select('id')
          .eq('slug', slug)
          .neq('id', updateData.id)
          .single();

        if (existingSlug) {
          throw new Error('Bu isimde bir kategori zaten mevcut');
        }
      }

      // Prepare update data
      const { id, ...dataToUpdate } = updateData;
      const finalUpdateData = {
        ...dataToUpdate,
        slug,
        updated_at: new Date().toISOString(),
      };

      // Update category
      const { data, error } = await this.supabase
        .from('categories')
        .update(finalUpdateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        this.handleError(error, 'updateCategory');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'update_category',
        'category',
        id,
        { old_data: existingCategory, new_data: finalUpdateData }
      );

      return data as Category;
    } catch (error) {
      this.handleError(error, 'updateCategory');
    }
  }

  /**
   * Delete category
   */
  async deleteCategory(userId: string, categoryId: string): Promise<void> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'manage_categories');

      // Get existing category
      const existingCategory = await this.getCategoryById(categoryId);

      // Check if category has products
      const { data: products } = await this.supabase
        .from('products')
        .select('id')
        .eq('category_id', categoryId)
        .limit(1);

      if (products && products.length > 0) {
        throw new Error('Bu kategoriye ait ürünler bulunmaktadır. Önce ürünleri silin veya başka kategoriye taşıyın.');
      }

      // Check if category has subcategories
      const { data: subcategories } = await this.supabase
        .from('categories')
        .select('id')
        .eq('parent_id', categoryId)
        .limit(1);

      if (subcategories && subcategories.length > 0) {
        throw new Error('Bu kategorinin alt kategorileri bulunmaktadır. Önce alt kategorileri silin.');
      }

      // Delete category
      const { error } = await this.supabase
        .from('categories')
        .delete()
        .eq('id', categoryId);

      if (error) {
        this.handleError(error, 'deleteCategory');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'delete_category',
        'category',
        categoryId,
        { deleted_category: existingCategory }
      );
    } catch (error) {
      this.handleError(error, 'deleteCategory');
    }
  }

  /**
   * Toggle category status
   */
  async toggleCategoryStatus(userId: string, categoryId: string): Promise<Category> {
    try {
      // Get current category
      const category = await this.getCategoryById(categoryId);

      // Update status
      return await this.updateCategory(userId, {
        id: categoryId,
        is_active: !category.is_active,
      });
    } catch (error) {
      this.handleError(error, 'toggleCategoryStatus');
    }
  }

  /**
   * Get category hierarchy (parent-child relationships)
   */
  async getCategoryHierarchy(): Promise<Category[]> {
    try {
      const { data, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })
        .order('name_tr', { ascending: true });

      if (error) {
        this.handleError(error, 'getCategoryHierarchy');
      }

      return data as Category[];
    } catch (error) {
      this.handleError(error, 'getCategoryHierarchy');
    }
  }

  /**
   * Update category sort order
   */
  async updateCategorySortOrder(userId: string, categoryId: string, sortOrder: number): Promise<void> {
    try {
      await this.validateUserPermission(userId, 'manage_categories');

      const { error } = await this.supabase
        .from('categories')
        .update({ sort_order: sortOrder })
        .eq('id', categoryId);

      if (error) {
        this.handleError(error, 'updateCategorySortOrder');
      }

      await this.logAdminActivity(
        userId,
        'update_category_sort_order',
        'category',
        categoryId,
        { sort_order: sortOrder }
      );
    } catch (error) {
      this.handleError(error, 'updateCategorySortOrder');
    }
  }
}
