import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

export abstract class BaseService {
  protected supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Generic error handler
   */
  protected handleError(error: any, operation: string): never {
    console.error(`${this.constructor.name} - ${operation}:`, error);
    
    if (error.code === 'PGRST116') {
      throw new Error('Kayıt bulunamadı');
    }
    
    if (error.code === '23505') {
      throw new Error('Bu kayıt zaten mevcut');
    }
    
    if (error.code === '23503') {
      throw new Error('Bu kayıt başka kayıtlar tarafından kullanılıyor');
    }
    
    if (error.code === '42501') {
      throw new Error('Bu işlem için yetkiniz bulunmuyor');
    }
    
    throw new Error(error.message || `${operation} işlemi sırasında hata oluştu`);
  }

  /**
   * Validate required fields
   */
  protected validateRequired(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => {
      const value = data[field];
      return value === null || value === undefined || (typeof value === 'string' && value.trim() === '');
    });

    if (missingFields.length > 0) {
      throw new Error(`Gerekli alanlar eksik: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Validate user permissions
   */
  protected async validateUserPermission(userId: string, permission: string): Promise<void> {
    const { data: user, error } = await this.supabase
      .from('users')
      .select('role, role_permissions')
      .eq('clerk_id', userId)
      .single();

    if (error) {
      throw new Error('Kullanıcı bilgileri alınamadı');
    }

    if (!user) {
      throw new Error('Kullanıcı bulunamadı');
    }

    // Admin her şeyi yapabilir
    if (user.role === 'admin') {
      return;
    }

    // Diğer roller için permission kontrolü
    if (!user.role_permissions?.includes(permission)) {
      throw new Error('Bu işlem için yetkiniz bulunmuyor');
    }
  }

  /**
   * Log admin activity
   */
  protected async logAdminActivity(
    adminId: string,
    action: string,
    targetType: string,
    targetId: string,
    details?: any
  ): Promise<void> {
    try {
      await this.supabase
        .from('admin_activity_log')
        .insert({
          admin_id: adminId,
          action,
          target_type: targetType,
          target_id: targetId,
          details: details || {},
        });
    } catch (error) {
      // Log hatası ana işlemi etkilemez
      console.warn('Admin activity log failed:', error);
    }
  }

  /**
   * Generate unique slug
   */
  protected generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  /**
   * Generate unique SKU
   */
  protected async generateSKU(prefix: string = 'PRD'): Promise<string> {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${timestamp}-${random}`;
  }
}
