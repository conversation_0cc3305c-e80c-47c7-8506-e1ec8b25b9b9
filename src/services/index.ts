import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';
import { CategoryService } from './category.service';
import { ProductService } from './product.service';

/**
 * Service Factory - Tüm servisleri merkezi olarak yönetir
 */
export class ServiceFactory {
  private static instance: ServiceFactory;
  private supabase: SupabaseClient<Database>;
  
  // Service instances
  private _categoryService?: CategoryService;
  private _productService?: ProductService;

  private constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Singleton pattern - tek instance oluştur
   */
  public static getInstance(supabase: SupabaseClient<Database>): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory(supabase);
    }
    return ServiceFactory.instance;
  }

  /**
   * Category Service
   */
  public get categoryService(): CategoryService {
    if (!this._categoryService) {
      this._categoryService = new CategoryService(this.supabase);
    }
    return this._categoryService;
  }

  /**
   * Product Service
   */
  public get productService(): ProductService {
    if (!this._productService) {
      this._productService = new ProductService(this.supabase);
    }
    return this._productService;
  }

  /**
   * Reset all services (useful for testing or when supabase client changes)
   */
  public reset(): void {
    this._categoryService = undefined;
    this._productService = undefined;
  }
}

// Export service types
export type { CreateCategoryData, UpdateCategoryData, CategoryFilters, Category } from './category.service';
export type { CreateProductData, UpdateProductData, ProductFilters, Product } from './product.service';

// Export services
export { CategoryService } from './category.service';
export { ProductService } from './product.service';
export { BaseService } from './base.service';
