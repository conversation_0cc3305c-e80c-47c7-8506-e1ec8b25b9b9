import { BaseService } from './base.service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';
import * as Crypto from 'expo-crypto';

export interface CreateProductData {
  name_tr: string;
  name_en?: string;
  description_tr: string;
  description_en?: string;
  image: string;
  price: number;
  currency?: string;
  stock: number;
  category_id?: string;
  is_available?: boolean;
  is_featured?: boolean;
  is_private?: boolean;
  tags?: string[];
  weight?: number;
  dimensions?: string;
  brand?: string;
  model?: string;
  warranty_months?: number;
  min_stock_level?: number;
  max_stock_level?: number;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface ProductFilters {
  search?: string;
  category_id?: string;
  is_available?: boolean;
  is_featured?: boolean;
  is_private?: boolean;
  brand?: string;
  min_price?: number;
  max_price?: number;
  tags?: string[];
  limit?: number;
  offset?: number;
  sort_by?: 'name' | 'price' | 'created_at' | 'sales_count' | 'view_count';
  sort_order?: 'asc' | 'desc';
}

export interface Product {
  id: string;
  name_tr: string;
  name_en?: string;
  description_tr: string;
  description_en?: string;
  image: string;
  price: number;
  currency: string;
  stock: number;
  category_id?: string;
  is_available: boolean;
  is_featured: boolean;
  is_private: boolean;
  tags?: string[];
  weight?: number;
  dimensions?: string;
  brand?: string;
  model?: string;
  warranty_months?: number;
  sku?: string;
  min_stock_level: number;
  max_stock_level?: number;
  sales_count: number;
  view_count: number;
  created_at: string;
  updated_at?: string;
  categories?: {
    id: string;
    name_tr: string;
    name_en?: string;
  };
}

export class ProductService extends BaseService {
  constructor(supabase: SupabaseClient<Database>) {
    super(supabase);
  }

  /**
   * Get all products with filters
   */
  async getProducts(filters: ProductFilters = {}): Promise<Product[]> {
    try {
      let query = this.supabase
        .from('products')
        .select(`
          *,
          categories(id, name_tr, name_en)
        `);

      // Apply filters
      if (filters.search) {
        query = query.or(`name_tr.ilike.%${filters.search}%,name_en.ilike.%${filters.search}%,brand.ilike.%${filters.search}%`);
      }

      if (filters.category_id) {
        query = query.eq('category_id', filters.category_id);
      }

      if (filters.is_available !== undefined) {
        query = query.eq('is_available', filters.is_available);
      }

      if (filters.is_featured !== undefined) {
        query = query.eq('is_featured', filters.is_featured);
      }

      if (filters.is_private !== undefined) {
        query = query.eq('is_private', filters.is_private);
      }

      if (filters.brand) {
        query = query.eq('brand', filters.brand);
      }

      if (filters.min_price !== undefined) {
        query = query.gte('price', filters.min_price);
      }

      if (filters.max_price !== undefined) {
        query = query.lte('price', filters.max_price);
      }

      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      // Sorting
      const sortBy = filters.sort_by || 'created_at';
      const sortOrder = filters.sort_order || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        this.handleError(error, 'getProducts');
      }

      return data as Product[];
    } catch (error) {
      this.handleError(error, 'getProducts');
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select(`
          *,
          categories(id, name_tr, name_en)
        `)
        .eq('id', id)
        .single();

      if (error) {
        this.handleError(error, 'getProductById');
      }

      if (!data) {
        throw new Error('Ürün bulunamadı');
      }

      // Increment view count
      await this.incrementViewCount(id);

      return data as Product;
    } catch (error) {
      this.handleError(error, 'getProductById');
    }
  }

  /**
   * Create new product
   */
  async createProduct(userId: string, productData: CreateProductData): Promise<Product> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'create_products');

      // Validate required fields
      this.validateRequired(productData, ['name_tr', 'description_tr', 'image', 'price', 'stock']);

      // Validate business rules
      if (productData.price <= 0) {
        throw new Error('Ürün fiyatı 0\'dan büyük olmalıdır');
      }

      if (productData.stock < 0) {
        throw new Error('Stok miktarı negatif olamaz');
      }

      // Generate SKU
      const sku = await this.generateSKU('PRD');

      // Prepare data
      const dataToInsert = {
        id: Crypto.randomUUID(),
        name_tr: productData.name_tr,
        name_en: productData.name_en || productData.name_tr,
        description_tr: productData.description_tr,
        description_en: productData.description_en || productData.description_tr,
        image: productData.image,
        price: productData.price,
        currency: productData.currency || 'TRY',
        stock: productData.stock,
        category_id: productData.category_id,
        is_available: productData.is_available ?? true,
        sku,
      };

      // Insert product
      const { data, error } = await this.supabase
        .from('products')
        .insert(dataToInsert)
        .select(`
          *,
          categories(id, name_tr, name_en)
        `)
        .single();

      if (error) {
        this.handleError(error, 'createProduct');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'create_product',
        'product',
        data.id,
        { product_data: dataToInsert }
      );

      return data as Product;
    } catch (error) {
      this.handleError(error, 'createProduct');
    }
  }

  /**
   * Update product
   */
  async updateProduct(userId: string, updateData: UpdateProductData): Promise<Product> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'update_products');

      // Validate required fields
      this.validateRequired(updateData, ['id']);

      // Get existing product
      const existingProduct = await this.getProductById(updateData.id);

      // Validate business rules
      if (updateData.price !== undefined && updateData.price <= 0) {
        throw new Error('Ürün fiyatı 0\'dan büyük olmalıdır');
      }

      if (updateData.stock !== undefined && updateData.stock < 0) {
        throw new Error('Stok miktarı negatif olamaz');
      }

      // Prepare update data
      const { id, ...dataToUpdate } = updateData;
      const finalUpdateData = {
        ...dataToUpdate,
        updated_at: new Date().toISOString(),
      };

      // Update product
      const { data, error } = await this.supabase
        .from('products')
        .update(finalUpdateData)
        .eq('id', id)
        .select(`
          *,
          categories(id, name_tr, name_en)
        `)
        .single();

      if (error) {
        this.handleError(error, 'updateProduct');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'update_product',
        'product',
        id,
        { old_data: existingProduct, new_data: finalUpdateData }
      );

      return data as Product;
    } catch (error) {
      this.handleError(error, 'updateProduct');
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(userId: string, productId: string): Promise<void> {
    try {
      // Validate permissions
      await this.validateUserPermission(userId, 'delete_products');

      // Get existing product
      const existingProduct = await this.getProductById(productId);

      // Check if product is in any orders
      const { data: orderItems } = await this.supabase
        .from('order_items')
        .select('id')
        .eq('product_id', productId)
        .limit(1);

      if (orderItems && orderItems.length > 0) {
        throw new Error('Bu ürün siparişlerde kullanılmaktadır. Ürün silinemez.');
      }

      // Check if product is in any carts
      const { data: cartItems } = await this.supabase
        .from('cart_items')
        .select('id')
        .eq('product_id', productId)
        .limit(1);

      if (cartItems && cartItems.length > 0) {
        // Remove from carts instead of blocking deletion
        await this.supabase
          .from('cart_items')
          .delete()
          .eq('product_id', productId);
      }

      // Delete product
      const { error } = await this.supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) {
        this.handleError(error, 'deleteProduct');
      }

      // Log activity
      await this.logAdminActivity(
        userId,
        'delete_product',
        'product',
        productId,
        { deleted_product: existingProduct }
      );
    } catch (error) {
      this.handleError(error, 'deleteProduct');
    }
  }

  /**
   * Toggle product availability
   */
  async toggleProductAvailability(userId: string, productId: string): Promise<Product> {
    try {
      // Get current product
      const product = await this.getProductById(productId);

      // Update availability
      return await this.updateProduct(userId, {
        id: productId,
        is_available: !product.is_available,
      });
    } catch (error) {
      this.handleError(error, 'toggleProductAvailability');
    }
  }

  /**
   * Update stock
   */
  async updateStock(userId: string, productId: string, newStock: number): Promise<Product> {
    try {
      if (newStock < 0) {
        throw new Error('Stok miktarı negatif olamaz');
      }

      return await this.updateProduct(userId, {
        id: productId,
        stock: newStock,
      });
    } catch (error) {
      this.handleError(error, 'updateStock');
    }
  }

  /**
   * Increment view count
   */
  private async incrementViewCount(productId: string): Promise<void> {
    try {
      await this.supabase.rpc('increment_product_view_count', {
        product_id: productId
      });
    } catch (error) {
      // View count increment failure shouldn't affect main operation
      console.warn('Failed to increment view count:', error);
    }
  }

  /**
   * Increment sales count
   */
  async incrementSalesCount(productId: string, quantity: number = 1): Promise<void> {
    try {
      await this.supabase.rpc('increment_product_sales_count', {
        product_id: productId,
        quantity: quantity
      });
    } catch (error) {
      console.warn('Failed to increment sales count:', error);
    }
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(): Promise<Product[]> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select(`
          *,
          categories(id, name_tr, name_en)
        `)
        .filter('stock', 'lte', 'min_stock_level')
        .eq('is_available', true)
        .order('stock', { ascending: true });

      if (error) {
        this.handleError(error, 'getLowStockProducts');
      }

      return data as Product[];
    } catch (error) {
      this.handleError(error, 'getLowStockProducts');
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit: number = 10): Promise<Product[]> {
    try {
      return await this.getProducts({
        is_featured: true,
        is_available: true,
        limit,
        sort_by: 'view_count',
        sort_order: 'desc',
      });
    } catch (error) {
      this.handleError(error, 'getFeaturedProducts');
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(categoryId: string, limit?: number): Promise<Product[]> {
    try {
      return await this.getProducts({
        category_id: categoryId,
        is_available: true,
        limit,
        sort_by: 'created_at',
        sort_order: 'desc',
      });
    } catch (error) {
      this.handleError(error, 'getProductsByCategory');
    }
  }
}
